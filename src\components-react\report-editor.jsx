// 必须要手动引入 React，否则会报错
import React from 'react';
import { Editor } from 'amis-editor';
import { Message } from 'element-ui';
import 'amis/lib/themes/cxd.css';
import 'amis-ui/lib/themes/cxd.css';
import '@/assets/css/override-amis.less';
import 'amis/lib/helper.css';
// import './amis-iconfont/iconfont.css';
import 'amis-editor-core/lib/style.css';
/**
 * amis底层是通过import()的方式引入monaco-editor，在本项目中会报错
 * 所以这里需要手动引入monaco-editor，避免报错
 */
// eslint-disable-next-line no-unused-vars
import monaco from 'monaco-editor';
import { axios } from '@quanzhiFE/qz-frontend';
import './qz-components/json-scheam/render';
import { QzJsonSchemaPlugin } from './qz-components/json-scheam/plugin';
import './qz-components/asset-sance/render';
import { AssetSancePlugin } from './qz-components/asset-sance/plugin';
import './qz-components/class-level/render';
import { ClassLevelPlugin } from './qz-components/class-level/plugin';
import './qz-components/data-lable/render';
import { DataLabelPlugin } from './qz-components/data-lable/plugin';
import './qz-components/export-com/render';
import { ExportComPlugin } from './qz-components/export-com/plugin';
import './qz-components/qz-table/plugins';
import './qz-components/qz-table/renders';
import './qz-components/amis-tree/render';
import { TreeComPlugin } from './qz-components/amis-tree/pulgin';
import { SamplePlugin } from './qz-components/samples/plugin';
import './qz-components/samples/render';
import { BarChartPlugin } from './qz-components/echarts/bar/bar-chart-plugin';
import './qz-components/echarts/bar/bar-chart-render';
import { PieChartPlugin } from './qz-components/echarts/pie/pie-chart-plugin';
import './qz-components/echarts/pie/pie-chart-render';
import { StaticTablePlugin } from './qz-components/static-table/plugin';
import './qz-components/static-table/render';
import { CardPlugin } from './qz-components/card/plugin';
import './qz-components/card/render';
import { TaskAnalysePlugin } from './qz-components/task-analyse/plugin';
import './qz-components/task-analyse/render';
export default function editor({ schema, onChange }) {
  return (
    <Editor
      showCustomRenderersPanel={true}
      value={schema}
      onChange={onChange}
      plugins={[
        QzJsonSchemaPlugin,
        AssetSancePlugin,
        ClassLevelPlugin,
        DataLabelPlugin,
        ExportComPlugin,
        TreeComPlugin,
        SamplePlugin,
        BarChartPlugin,
        PieChartPlugin,
        StaticTablePlugin,
        CardPlugin,
        TaskAnalysePlugin
      ]}
      amisEnv={{
        fetcher: axios,
        notify: (type, msg) => {
          Message[type](msg);
        },
        alert: (msg) => {
          alert(msg);
        }
      }}
    ></Editor>
  );
}
