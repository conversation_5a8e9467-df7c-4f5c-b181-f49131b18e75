import { BasePlugin, getEventControlConfig, getSchemaTpl } from 'amis-editor';
import { method } from 'lodash-es';
const rendererName = 'qz-export-file-render';
export class ExportComPlugin extends BasePlugin {
  // 这里要跟对应的渲染器名字对应上
  // 注册渲染器的时候会要求指定渲染器名字
  rendererName = rendererName;
  // 暂时只支持这个，配置后会开启代码编辑器
  $schema = '/schemas/UnknownSchema.json';
  //组件关键字，用来辅助组件列表搜索
  searchKeywords = '';
  // 用来配置名称和描述
  name = '导入组件';
  title = '';
  description = '导入组件';

  // tag，决定会在哪个 tab 下面显示的
  tags = ['全知组件'];

  // 图标
  icon = 'fa fa-user';

  // 用来生成预览图的
  previewSchema = {
    type: rendererName
  };

  // 拖入组件里面时的初始数据
  scaffold = {
    type: rendererName,
    executorType: '',
    exportName: '',
    importType: '',
    dialogType: '' //用于区分什么弹框的导入，根据不同的值显示不同的弹框描述，如果为空，则默认展示导入资产的弹框描述
  };

  // 事件定义
  events = [
    {
      eventName: 'save',
      eventLabel: '保存成功',
      description: '保存成功时触发'
    },
    {
      eventName: 'cancel',
      eventLabel: '取消',
      description: '取消时触发'
    }
  ];

  // 右侧面板相关
  panelBodyCreator = (context) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: [
          getSchemaTpl('description', {
            name: 'executorType',
            label: 'executorType',
            maxRows: 10,
            rows: 10,
            placeholder: '请输入executorType的值'
          }),
          getSchemaTpl('description', {
            name: 'exportName',
            label: '导出参数name',
            maxRows: 10,
            rows: 10,
            placeholder: '请输入name的值'
          }),
          getSchemaTpl('description', {
            name: 'importType',
            label: 'importType',
            maxRows: 10,
            rows: 10,
            placeholder: '请输入importType的值'
          }),
          // getSchemaTpl('description', {
          //   name: 'method',
          //   label: '下载模版接口请求方式',
          //   maxRows: 10,
          //   rows: 10,
          //   placeholder: '请输入下载模版接口请求方式'
          // }),
          getSchemaTpl('description', {
            name: 'dialogType',
            label: '弹框类型-默认展示资产导入',
            maxRows: 10,
            rows: 10,
            placeholder:
              '请输入弹框类型,(资产-asset、服务-serve、标签-lable、资产描述-assetDesc、资产分类-assetClass)'
          })
        ]
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };
}
