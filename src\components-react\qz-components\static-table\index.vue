<template>
  <qz-pro-table
    :data-source="dataList"
    :pageVisible="false"
  >
    <qz-table-column
      v-for="item in cols"
      :key="item.prop"
      :label="item.label"
      :prop="item.prop"
    />
  </qz-pro-table>
</template>

<script>
export default {
  props: ["props"],
  data () {
    return {
      cols: [],
      dataList: []
    };
  },

  components: {},

  mounted () {
    this.$nextTick(() => {
      const { cols, comdata } = this.props;
      if (cols && cols.length > 0) {
        this.cols = cols || [];
        this.dataList = comdata || [];
        if (typeof cols === "string") {
          this.cols = JSON.parse(cols);
        }
        if (typeof comdata === "string") {
          this.dataList = JSON.parse(comdata);
        }
        console.log(this.dataList, 'dataList');

      } else {
        this.cols = [
          { "label": "列1", "prop": "col1" },
          { "label": "列2", "prop": "col2" },
          { "label": "列3", "prop": "col3" }
        ]
        this.dataList = [
          { "col1": "数据1", "col2": "数据2", "col3": "数据3" },
          { "col1": "数据4", "col2": "数据5", "col3": "数据6" },
          { "col1": "数据7", "col2": "数据8", "col3": "数据9" }
        ]
      }
    });
  },

  methods: {}
}

</script>
<style lang="less" scoped>
</style>