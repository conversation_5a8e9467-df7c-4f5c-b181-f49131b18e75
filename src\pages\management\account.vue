<template>
  <div class="account">
    <api-table
      ref="table"
      table-id="account-list"
      :data-source="getDataList"
      :search-input-options="{
        key: 'username',
        placeholder: '请输入账号名进行筛选'
      }"
      title="账号列表"
      tools-layout="searchInput,divider,refresh,colConfig, add"
      @selection-change="handleSelectionChange"
    >
      <api-table-tool-register id="add">
        <el-button type="primary" size="mini" @click="showDetail({})">
          新增账号
        </el-button>
      </api-table-tool-register>
      <!--      <api-table-column-->
      <!--        :selectable="(row) => !row.defaultFlag"-->
      <!--        type="selection"-->
      <!--        fixed="left"-->
      <!--      ></api-table-column>-->
      <api-table-column label="账号名" prop="username">
        <template #default="{ row }">
          <span class="action-link" @click="showDetail(row)">{{
            row.username
          }}</span>
        </template>
      </api-table-column>
      <api-table-column label="角色" prop="roleList">
        <template slot-scope="{ row }">
          <span v-if="row.roleList.length > 0">{{
            row.roleList[0].roleName
          }}</span>
          <span v-else>--</span>
        </template>
      </api-table-column>
      <!-- <api-table-column label="数据权限" prop="roleList">
        <tempalte #default="row">
          {{ row.type===1:'研发组':'业务组' }}
        </template>
        <template slot-scope="{ row }">
          <span v-if="row.roleList.length > 0">{{
            row.roleList[0].roleName
          }}</span>
          <span v-else>--</span>
        </template>
      </api-table-column> -->
      <api-table-column
        label="账号描述"
        prop="remark"
        :default-shown="false"
      ></api-table-column>
      <api-table-column
        label="有效期 "
        prop="expireTime"
        formatter="formatTime"
      >
        <template #default="{ row }">
          {{ row.expireTime ? formatTime(row.expireTime) : '永久有效' }}
        </template>
      </api-table-column>
      <api-table-column label="锁定状态" prop="status">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="mini">{{
            row.status === 1 ? '未锁定' : '锁定'
          }}</el-tag>
        </template>
      </api-table-column>
      <api-table-column label="创建时间" prop="createTime"></api-table-column>
      <api-table-column
        label="更新时间"
        prop="updateTime"
        :default-shown="false"
      ></api-table-column>
      <api-table-column label="操作" width="80" fixed="right">
        <template #default="{ row }">
          <span
            v-if="!row.defaultFlag"
            class="action-link el-icon-delete"
            @click="deleteItem(row)"
          ></span>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>

<script>
import { formatTime } from '@/utils/string-utils';
import {
  postAccountList,
  postDeleteAccountList
} from '@/service/account-service.js';
function notExpired(time) {
  if (!time) return true;
  const timestamp = new Date(time).valueOf();
  console.log(timestamp, new Date().getTime(), time);
  return timestamp > new Date().getTime();
}

export default {
  data() {
    return {
      selectList: []
    };
  },
  computed: {
    opeartions() {
      return [
        {
          name: '删除',
          disabled: !this.selectList.length,
          handler: () => {
            this.batchDelete();
          }
        }
      ];
    }
  },
  methods: {
    formatTime,
    getDataList(params) {
      const p = {
        username: params.username,
        pageNum: params.page,
        pageSize: params.limit
      };
      return postAccountList(p);
    },
    handleSelectionChange(rows) {
      this.selectList = rows;
    },
    batchDelete() {
      const notExpiredAccountNames = this.selectList
        .filter((item) => notExpired(item.expireTime))
        .map((item) => item.username);
      const ids = this.selectList.map((item) => item.id);
      if (notExpiredAccountNames.length) {
        this.$confirm(
          `账号${notExpiredAccountNames.join(',')}在有效期内，是否删除？`,
          {
            customClass: 'break-all'
          }
        ).then(() => {
          this.removeAccount(ids);
        });
      } else {
        this.removeAccount(ids);
      }
    },
    showDetail(row) {
      this.$DrawAlert({
        title: row?.id ? '编辑账号' : '新增账号',
        params: {
          detail: row || {},
          callback: () => {
            if (row.id) {
              this.$refs.table.reloadCurrentPage();
            } else {
              this.$refs.table.reload();
            }
          }
        },
        componentObj: {
          component: () => import('./packages/account-detail.vue')
        }
      });
    },
    deleteItem(row) {
      if (notExpired(row.expireTime)) {
        this.$confirm(`账号${row.username}在有效期内，是否删除？`, {
          customClass: 'break-all'
        }).then(() => {
          this.removeAccount([row.id]);
        });
      } else {
        this.removeAccount([row.id]);
      }
    },
    removeAccount(ids) {
      postDeleteAccountList({ ids: ids })
        .then((res) => {
          this.$message.success('删除成功');
          this.$refs.table.reload();
        })
        .catch((err) => this.$message.error(err.msg || '删除失败'));
    }
  }
};
</script>

<style lang="less" scoped></style>
