<template>
  <div class="role">
    <api-table
      ref="table"
      table-id="role-list"
      :data-source="getDataList"
      :search-input-options="{
        key: 'roleName',
        placeholder: '请输入角色名称进行筛选'
      }"
      title="角色列表"
      tools-layout="searchInput,divider,refresh,divider,add,divider"
      @selection-change="handleSelectionChange"
    >
      <!-- add -->
      <api-table-tool-register id="add">
        <el-button type="primary" size="mini" @click="showDetail({})">
          新增角色
        </el-button>
      </api-table-tool-register>
      <api-table-column type="selection">
        <tempalte #default="{ row }">
          <span v-if="row.type === ROLE_TYPE_CUSTOM"></span>
        </tempalte>
      </api-table-column>
      <api-table-column label="角色名称" prop="roleName">
        <template #default="{ row }">
          <!-- @click="showDetail(row)" -->
          <span @click="showDetail(row)">{{ row.roleName }}</span>
        </template>
      </api-table-column>
      <api-table-column label="角色描述" prop="remark"></api-table-column>
      <api-table-column label="操作" width="80" fixed="right">
        <template #default="{ row }">
          <span
            v-if="row.type===ROLE_TYPE_CUSTOM"
            class="action-link el-icon-delete"
            @click="deleteItem(row)"
          ></span>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>

<script>
import {
  ROLE_TYPE_CUSTOM,
  ROLE_TYPE_INTERNAL
} from '@/constant/common-constants';
import { postRoleList } from '@/service/role-service';

export default {
  data() {
    return {
      selectList: [],
      ROLE_TYPE_CUSTOM
    };
  },
  computed: {
    opeartions() {
      return [
        {
          name: '删除',
          disabled: !this.selectList.length,
          handler: () => {
            this.batchDelete();
          }
        }
      ];
    }
  },
  methods: {
    getDataList(params) {
      const p = {
        roleName: params.roleName,
        pageNum: params.page,
        pageSize: params.limit
      };
      return postRoleList(p);
    },
    handleSelectionChange(rows) {
      this.selectList = rows.map((item) => item.id);
    },
    batchDelete() {
      console.log(this.selectList);
      this.$confirm(
        '删除角色后，角色下的账号全部都会被删除掉，确认删除？'
      ).then(() => {
        this.removeRole(this.selectList);
      });
    },
    showDetail(row) {
      if (row.type === ROLE_TYPE_INTERNAL) {
        this.$message.warning('内置角色无法编辑');
        return;
      }
      this.$DrawAlert({
        title: row.id ? '编辑角色' : '新增角色',
        customClass: 'overflow-hidden-drawer',
        params: {
          detail: row || {},
          callback: () => {
            if (row.id) {
              this.$refs.table.reloadCurrentPage();
            } else {
              this.$refs.table.reload();
            }
          }
        },
        // component: () => import('./packages/role-detail.vue')

        componentObj: {
          component: () => import('./packages/role-detail.vue')
        }
      });
    },
    removeRole(ids) {
      // this.$refs.table.loading = true;
    },
    deleteItem(id) {
      // this.$refs.table.loading = true;
    }
  }
};
</script>

<style lang="less" scoped></style>
