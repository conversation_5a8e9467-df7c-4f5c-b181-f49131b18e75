<template>
  <div class="assign-account">
    <el-form ref="form" :model="formInfo" :rules="rules">
      <el-form-item prop="accountIds" label="账号名称：">
        <el-select
          v-model="formInfo.accountIds"
          placehoder="请选择账号"
          class="full-width"
          multiple
          clearable
          collapse-tags
        >
          <el-option
            v-for="account in accountList"
            :key="account.id"
            :value="account.id"
            :label="account.username"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="footer-buttons">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save" :loading="saveLoading"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
import { postAccountList } from '@/service/account-service';
import { postDataManageAssignAccount,postDataManageBatchAssignAccount } from '@/service/data-manage-service';
export default {
  props: ['params'],
  data() {
    return {
      formInfo: {
        accountIds: []
      },
      rules: {
        accountIds: [
          { required: true, message: '请选择账号', trigger: 'change' }
        ]
      },
      accountList: [],
      saveLoading: false
    };
  },

  async mounted() {
    await this.loadAccountList();
    await this.loadAssignedAccounts();
    if (!this.isBatchMode && this.params.detail?.userIds) {
      this.formInfo.userIds = this.params.detail.userIds;
    }
  },
  computed: {
    isBatchMode() {
      return this.params.selectedRows && this.params.selectedRows.length > 0;
    }
  },
  methods: {
    async loadAccountList() {
      try {
        const res = await postAccountList({
          pageNum: 1,
          pageSize: 5000
        });
        this.accountList = res.data?.rows || [];
      } catch (error) {
        this.$message.error('获取账号列表失败');
      }
    },
    loadAssignedAccounts() {
      this.formInfo.accountIds = this.params.detail?.userIds || [];
    },

    async save() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return;
 if (this.isBatchMode) {
          this.$confirm('确定为所选分组分配账号吗？').then(() => {
            this.doSave();
          });
        } else {
          this.doSave();
        }
      });
    },
     async doSave() {
      this.saveLoading = true;
      try {
        if (this.isBatchMode) {
          const groupIds = this.params.selectedRows.map(group => group.id);
          await postDataManageBatchAssignAccount({
            groupIds: groupIds,
            userIds: this.formInfo.userIds
          });
          this.$message.success('批量分配成功');
        } else {
          await postDataManageAssignAccount({
            groupId: this.params.detail.id,
            userIds: this.formInfo.userIds
          });
          this.$message.success('分配成功');
        }

        this.params.callback && this.params.callback();
        this.params.close();
      } catch (error) {
        this.$message.error(this.isBatchMode ? '批量分配失败' : '分配失败');
      } finally {
        this.saveLoading = false;
      }
    },

    cancel() {
      this.params.close();
    }
  }
};
</script>

<style lang="less" scoped>
.assign-account {
  padding: 20px;
}

.full-width {
  width: 100%;
}

.footer-buttons {
  text-align: center;
  margin-top: 20px;
}
</style>
