{"name": "data-map", "version": "1.0.0", "private": true, "scripts": {"serve": "rsbuild dev", "build": "rsbuild build", "preview": "rsbuild preview", "lint": "eslint --ext .js,jsx,.vue --ignore-path .gitignore --fix src", "prepare": "husky", "build-module": "npm install @quanzhiFE/qz-frontend && sh ./scripts/build-module.sh", "serve-module": "sh ./scripts/serve-module.sh", "change-version": "npm --no-git-tag-version version", "version": "node ./scripts/change-version.js", "prettier": "prettier --write \"src/**/*.{js,jsx,vue}\""}, "dependencies": {"@dineug/erd-editor": "^3.2.7", "@lljj/vue-json-schema-form": "^1.19.0", "@quanzhiFE/monaco-editor": "^0.52.0", "@quanzhiFE/qz-frontend": "^0.0.4", "@vueblocks/elp-cascader": "^0.2.1", "amis": "6.11.0", "amis-core": "6.11.0", "amis-editor": "6.11.0", "amis-editor-core": "6.11.0", "amis-formula": "6.11.0", "amis-ui": "6.11.0", "awe-dnd": "^0.3.4", "axios": "^0.19.2", "brace": "^0.11.1", "codemirror": "^5.65.12", "crypto-js": "^4.0.0", "deep-diff": "^1.0.2", "echarts": "^5.2.0", "element-resize-detector": "^1.2.4", "element-ui": "^2.15.6", "eslint-plugin-react-hooks": "^5.1.0", "less-loader": "^6.1.2", "lodash-es": "^4.17.21", "md5": "^2.2.1", "mobx": "4.15.7", "mobx-react": "6.3.1", "mobx-state-tree": "3.17.3", "moment": "^2.30.1", "monaco-editor": "0.52.0", "monaco-editor-webpack-plugin": "7.1.0", "nprogress": "^0.2.0", "parse5": "^5.1.1", "qiankun": "^2.8.4", "qrcode": "^1.4.4", "react": "^16.14.0", "react-dom": "^16.14.0", "screenfull": "^6.0.2", "sql-formatter": "^15.6.5", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.11", "utf8": "^3.0.0", "v-jsoneditor": "^1.4.1", "v-scale-screen": "^1.0.2", "v-viewer": "^1.6.4", "viewerjs": "^1.11.6", "vue": "^2.7.14", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-router": "^3.1.6", "vue-select": "^3.11.2", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vxe-table": "~3.6.6", "xe-utils": "^3.5.1", "xss": "^1.0.9"}, "devDependencies": {"@quanzhiFE/git-commit-info-webpack-plugin": "^1.0.0", "@rsbuild/core": "^1.3.22", "@rsbuild/plugin-babel": "^1.0.5", "@rsbuild/plugin-basic-ssl": "^1.1.1", "@rsbuild/plugin-eslint": "^1.1.1", "@rsbuild/plugin-less": "^1.2.4", "@rsbuild/plugin-vue2": "^1.0.3", "@rsbuild/plugin-vue2-jsx": "^1.0.3", "@vue/cli-plugin-vuex": "~5.0.4", "babel-eslint": "^10.0.3", "compression-webpack-plugin": "^11.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-vue": "^9.32.0", "husky": "^9.1.7", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^3.3.2", "typescript": "^5.8.3", "vue-eslint-parser": "^9.4.3"}, "lint-staged": {"*.(vue|js|jsx)": ["npm run lint", "npm run prettier"]}}