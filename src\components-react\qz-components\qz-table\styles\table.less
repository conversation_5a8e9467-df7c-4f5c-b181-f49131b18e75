.ApiTable__group-results {
  display: flex;
  margin-bottom: 10px;
  .ApiTable__group-name {
    flex: none;
    font-size: 14px;
    color: @text-primary-color;
  }
  .ApiTable__group-list {
    display: inline-block;
    .ApiTable__group-option {
      display: inline-block;
      box-sizing: border-box;
      margin: 0 10px;
      color: @text-primary-color;
      cursor: pointer;
      transition: color 0.4s;
      border-bottom: 2px solid transparent;
      &.selected {
        font-weight: 600;
        border-bottom: 2px solid @theme-blue;
      }
      &:hover {
        color: @theme-blue;
      }
    }
  }
  .ApiTable__cancel-group {
    flex: none;
    margin-left: 10px;
    color: @theme-blue;
    cursor: pointer;
  }
}
.ApiTable__group-more {
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  cursor: pointer;
}
.ApiTable__group-more-popper {
  padding: 5px !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}
.ApiTable__group-more-option {
  box-sizing: border-box;
  border-radius: 3px;
  padding: 5px 10px;
  margin-bottom: 5px;
  cursor: pointer;
  &.selected,
  &:hover {
    color: @theme-blue;
  }
  &.selected {
    background-color: @bg-grey-color;
  }
}

.cxd-Table2 .cxd-Table-title {
  display: none;
}

.crud-with-view {
  display: flex;
  align-items: flex-start;
  height: 100%;

  .crud-view-wrapper {
    flex: none;
    height: 100%;
    overflow: hidden;
    min-width: 0;
  }

  .view-splitter {
    flex: none;
    width: 8px;
    height: 100%;
    background-color: transparent;
    margin-right: 20px;
    position: relative;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 1px;
      background-color: @table-border-color;
      transition: all 0.2s ease;
    }

    &:hover {
      &:not(.collapsed) {
        cursor: col-resize;
      }

      &::before {
        width: 5px;
        background-color: @shallow-border-color;
      }

      .view-toggle-button {
        opacity: 1;
        visibility: visible;
      }
    }

    &:active,
    &.dragging {
      &::before {
        background-color: @shallow-border-color;
        width: 2px;
      }
    }

    // 收起状态样式
    &.collapsed {
      cursor: pointer;

      &:hover {
        cursor: pointer;

        &::before {
          background-color: @shallow-border-color;
          width: 2px;
        }
      }
    }

    // 收起/展开按钮
    .view-toggle-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      height: 44px;
      width: 14px;
      background-color: @white-color;
      border: 1px solid @border-light-color;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      cursor: pointer;
      z-index: 10;
    }

    &.collapsed .view-toggle-button {
      opacity: 1;
      visibility: visible;
    }
  }

  .cxd-Crud2 {
    flex: auto;
    overflow: hidden;
  }
}
