import { types } from 'mobx-state-tree';
import { CRUDStore, registerStore } from 'amis-core';

// 继承CRUD的store配置，添加业务需要的属性
export const QzTableStore = CRUDStore.named('QzTableStore')
  .props({
    filterFormData: types.optional(types.frozen(), {}), // Using frozen to allow any JS value
    filterTags: types.optional(types.array(types.frozen()), []), // Using frozen to allow any JS value
    groupField: types.optional(types.frozen(), {}),
    groupResult: types.optional(types.array(types.frozen()), []),
    curGroup: types.optional(types.frozen(), '')
  })
  .actions((self) => ({
    setCurGroup(group) {
      self.curGroup = group;
    },
    setGroupField(field) {
      self.groupField = field;
    },
    setGroupResult(result) {
      self.groupResult = result;
    },
    cancelGroup() {
      self.curGroup = '';
      self.groupField = {};
      self.groupResult = [];
    },
    changeFilterFormData(name, value) {
      // Create a new copy of the object to ensure proper reactivity
      self.filterFormData = {
        ...self.filterFormData,
        [name]: value
      };
    },
    bulkChangeFilterFormData(data) {
      // Create a new merged object
      self.filterFormData = {
        ...self.filterFormData,
        ...data
      };
    },
    resetFilterFormData() {
      // 将 filterFormData 的每个key的值重置为 undefined
      self.filterFormData = {};
    },
    updateFilterTag(tag) {
      // 如果tag不存在，则添加到filterTags中
      if (
        !self.filterTags.some((existingTag) => existingTag.name === tag.name)
      ) {
        self.filterTags.push(tag);
      } else {
        // 如果tag已存在，则更新其值
        self.filterTags = self.filterTags.map((existingTag) =>
          existingTag.name === tag.name ? tag : existingTag
        );
      }
    },
    removeFilterTag(name) {
      // 从filterTags中移除指定name的tag
      self.filterTags = self.filterTags.filter((tag) => tag.name !== name);
      // 将对应name的filterFormData删掉
      // eslint-disable-next-line no-unused-vars
      const { [name]: _, ...rest } = self.filterFormData;
      self.filterFormData = rest;
    }
  }));

registerStore(QzTableStore);
