import { types } from 'mobx-state-tree';
import { CRUDStore, registerStore } from 'amis-core';

// 继承CRUD的store配置，添加业务需要的属性
export const QzTableStore = CRUDStore.named('QzTableStore')
  .props({
    filterFormVisible: types.optional(types.boolean, false), // 是否显示筛选表单
    filterFormData: types.optional(types.frozen(), {}), // Using frozen to allow any JS value
    groupField: types.optional(types.frozen(), {}),
    groupResult: types.optional(types.array(types.frozen()), []),
    curGroup: types.optional(types.frozen(), '')
  })
  .actions((self) => ({
    toggleFilterForm() {
      self.filterFormVisible = !self.filterFormVisible;
    },
    setCurGroup(group) {
      self.curGroup = group;
    },
    setGroupField(field) {
      self.groupField = field;
    },
    setGroupResult(result) {
      self.groupResult = result;
    },
    cancelGroup() {
      self.curGroup = '';
      self.groupField = {};
      self.groupResult = [];
    },
    changeFilterFormData(name, value) {
      // Create a new copy of the object to ensure proper reactivity
      self.filterFormData = {
        ...self.filterFormData,
        [name]: value
      };
    },
    bulkChangeFilterFormData(data) {
      // Create a new merged object
      self.filterFormData = {
        ...self.filterFormData,
        ...data
      };
    },
    resetFilterFormData() {
      // 将 filterFormData 的每个key的值重置为 undefined
      self.filterFormData = {};
    },
    removeFilterTag(name) {
      // 将对应name的filterFormData删掉
      // eslint-disable-next-line no-unused-vars
      const { [name]: _, ...rest } = self.filterFormData;
      self.filterFormData = rest;
    }
  }));

registerStore(QzTableStore);
