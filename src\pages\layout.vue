<!--
 * @Fileoverview: 默认页面布局
 * @Description: 顶部一级菜单，次顶部二级菜单，左侧三级菜单，右侧内容
-->
<template>
  <el-container>
    <el-header :height="`${headerHeight}px`">
      <qz-header
        @header-height="getHeaderHeight"
        @menu-change="getThirdMenuList"
        @tree-click="getTreeHighlight"
      />
    </el-header>
    <div class="system_tip" v-if="isShow == 'none'">
      <div class="flex">
        <span>请选择您需要的分类分级模版！</span>
        <div class="go_choose" @click="chooseTemplate">去选择</div>
      </div>
      <div class="close_tip" @click="close">不在提醒</div>
    </div>
    <el-container>
      <router-view></router-view>
    </el-container>
    <el-footer height="46px">
      <qz-footer></qz-footer>
    </el-footer>
    <exportDialog></exportDialog>
  </el-container>
</template>
<script>
import QzHeader from '@/components/qz-header.vue';
import QzFooter from '@/components/qz-footer.vue';
//配合amis导出文件操作，通过特定的事件触发
import exportDialog from '@/components-react/qz-components/export-com/export-dialog.vue';
import { PAGE_URL_AMIS_PRE } from '@/constant/page-url-constants';
import { postTemplateData } from '@/service/common-service';
import {
  setSystemTemplate,
  getSystemTemplate,
  getLocalUserInfo
} from '@/utils/storage-utils';
import { CHART_COLOR_TEXT_REGULAR_COLOR } from '@/constant/common-constants';
import { getLoginSecurityInfoById } from '@/service/login-service';
import watermark from '@/utils/watermark';
import moment from 'moment';
import { mapState } from 'vuex';
export default {
  components: { QzHeader, QzFooter, exportDialog },
  data() {
    return {
      headerHeight: 46,
      thirdMenuList: [],
      filterText: '',
      defaultExpandKeys: [],
      isShow: 'has',
      waterMarkSettings: {
        watermark_txt: '', //水印的内容
        watermark_fontsize: '10px',
        watermark_width: 200, //水印宽度
        watermark_height: 100, //水印长度
        watermark_x_space: 50, //水印x轴间隔
        watermark_y_space: 20, //水印y轴间隔 898989 10%
        watermark_color: CHART_COLOR_TEXT_REGULAR_COLOR,
        watermark_alpha: 0.3, //水印透明度，要求设置在大于等于0.005
        watermark_parent_node: null, //水印插件挂载的父元素element,不输入则默认挂在body上
        monitor: true, //monitor 是否监控， true: 不可删除水印; false: 可删水印。
        watermark_angle: 330 //水印倾斜度数
      }
    };
  },
  watch: {
    headerHeight(val) {
      this.headerHeight = val;
    },
    isOpenWatermark: {
      handler(val) {
        this.setWatermark();
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['isOpenWatermark'])
  },
  created() {
    this.setWatermark();
  },
  mounted() {
    const params = {
      searchConditionList: [{ fieldName: 'used', columnExp: '=', value: true }],
      columnList: ['id', 'name']
    };
    //请求是否有模版的接口
    postTemplateData(params).then((res) => {
      const temObj = {
        status: '',
        temName: ''
      };
      //处理两种不同的返回格式*************************************/
      const resData = res.data;
      if (Array.isArray(resData)) {
        this.isShow = 'none';
        temObj.status = this.isShow;
      } else if (res?.data?.id) {
        this.isShow = 'has';
        temObj.status = this.isShow;
        temObj.temName = res.data?.name || '';
      }
      setSystemTemplate(temObj);
    });

    //监听amis标签模版是否选中模版
    if (!this.temTimer) {
      this.temTimer = setInterval(() => {
        const templateObj = getSystemTemplate();
        if (templateObj) {
          if (templateObj.status == 'has' || templateObj.status == 'notip') {
            this.isShow = templateObj.status;
            clearInterval(this.temTimer);
            this.temTimer = null;
          }
        }
      }, 1000);
    }
  },
  methods: {
    setWatermark() {
      // 获取水印信息
      const { username } = getLocalUserInfo();
      getLoginSecurityInfoById('/pageWatermark')
        .then((res) => {
          const resData = res.data ? JSON.parse(res.data) : {};
          if (resData.isOpenWatermark) {
            this.waterMarkSettings.watermark_txt =
              username + moment().format('YYYYMMDD');
            watermark.init(this.waterMarkSettings);
          } else {
            watermark.remove();
          }
        })
        .catch((err) => {
          console.error(err);
          this.$message.error(err.msg || '获取是否打开水印数据失败');
        });
    },
    // openMenu(menu, openFirstChild = true) {
    //   if (!menu.children || menu.children.length === 0) {
    //     if (menu.entry !== this.$route.meta?.id) {
    //       this.$toPage({ id: menu.entry, type: menu.meta?.openType || '' });
    //     }
    //   } else if (openFirstChild) {
    //     this.openMenu(menu.children[0], openFirstChild);
    //   }
    // },
    // handleNodeClick(menu) {
    //   if (!menu.children) {
    //     this.openMenu(menu, false);
    //   }
    // },
    handleSearch() {
      this.$refs.tree.filter(this.filterText);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.includes(value);
    },
    getHeaderHeight(height) {
      this.headerHeight = height;
    },
    getThirdMenuList(menuList) {
      console.log(menuList, 'ffff');
      this.thirdMenuList = menuList;
    },
    getTreeHighlight(highLightData) {
      console.log(highLightData, 'jjjjjj');
      this.defaultExpandKeys = highLightData.defaultExpandKeys;

      // 要等菜单展开，dom元素才会渲染
      this.$nextTick(() => {
        const menuDoms =
          this.$refs.tree.$el.querySelectorAll(`.el-tree-node__content`) || [];
        menuDoms.forEach((dom) => {
          dom.classList.remove('active');
        });
        this.$refs.tree.$el
          .querySelector(
            `.custom-tree-node[data-id="${highLightData.lastMenuActive}"]`
          )
          .parentElement.classList.add('active');
      });
    },
    chooseTemplate() {
      window.open(`${PAGE_URL_AMIS_PRE}?acode=scan_tag`, '_self');
    },
    close() {
      this.isShow = 'notip';
      setSystemTemplate({
        status: this.isShow,
        temName: ''
      });
    }
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-container {
  padding: 10px 10px 0 10px;
  display: block;
}
.el-header {
  padding: 0;
  z-index: 100;
}
.qz-main {
  padding: 0;
}
.el-footer {
  padding: 0;
}
.system_tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #e94704f2;
  padding: 10px;
  color: #fff;
  .go_choose {
    cursor: pointer;
    color: #ffe83e;
    font-weight: bold;
  }
  .close_tip {
    cursor: pointer;
  }
}
.aside-menu {
  padding: 20px;
  .el-tree ::v-deep {
    .el-tree-node__expand-icon {
      color: @text-regular-color;
    }
    .el-tree-node__expand-icon.is-leaf {
      visibility: hidden;
    }
    .el-tree-node__content {
      height: 40px;
      line-height: 40px;
    }
    .el-tree-node__content.active {
      background: @bg-light-blue-color;
      color: @theme-color;
      border-radius: 5px;
    }
  }
}
</style>
