<template>
  <el-card class="box-card">
    <div :class="cardType=='normal'?'normal-card':'data-card'">
      {{comData||'--'}}
    </div>
    <div class="desc">
      {{desc || '--'}}
    </div>
  </el-card>
</template>

<script>
export default {
  props: ['props'],
  data () {
    return {
      comData: "",
      desc: "",
      cardType: ""
    };
  },

  components: {},

  mounted () {
    const { sql, comdata, desc, cardType } = this.props;
    this.cardType = cardType || "normal";
    this.comData = comdata || "";
    this.desc = desc || "";
  },

  methods: {}
}

</script>
<style lang='less' scoped>
.box-card {
  width: 100%;
  height: 100%;
  min-width: 100px;
  min-height: 100px;
  .normal-card {
    font-size: 14px;
    color: #000000;
    font-weight: bold;
    display: block;
    margin-bottom: 10px;
  }
  .data-card {
    font-size: 20px;
    color: #154be5;
    font-weight: bold;
    display: block;
    margin-bottom: 10px;
  }
  .desc {
    font-size: 14px;
    color: #666;
    display: block;
  }
}
</style>