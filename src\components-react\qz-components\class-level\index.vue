<template>
  <div class="class-level" v-loading="loading">
    <div class="tip-context mb20">
      <span v-if="templateName">
        当前使用的分类分级模版为：{{
          templateName
        }}模版。将对已选资产的分类分级结果进行全量覆盖，已锁定的分类分级结果不会被覆盖。
      </span>
      <span v-else>当前未使用任何分类分级模版。</span>
    </div>
    <el-form
      ref="classForm"
      :model="classForm"
      :rules="rules"
      :disabled="isDetail"
      label-position="left"
      label-width="100px"
      size="small"
    >
      <el-form-item label="任务名称:" prop="name">
        <el-input
          v-model="classForm.name"
          placeholder="请输入任务名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="任务描述:" prop="description">
        <el-input
          v-model="classForm.description"
          placeholder="请输入任务描述"
          clearable
        />
      </el-form-item>
      <el-form-item label="分级对象:" prop="target">
        <el-radio-group v-model="classForm.target" @change="changeVal">
          <el-radio
            v-for="item in classObjList"
            :label="item.value"
            :key="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
        <el-tooltip
          class="item"
          effect="dark"
          content=""
          placement="top"
          style="position: absolute; left: -26px; top: 8px"
        >
          <em class="el-icon-warning el-alert--info is-light"></em>
          <div slot="content" v-html="'对所选对象中的字段进行分类分级'"></div>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        v-if="classForm.target == 'DATA_SOURCE'"
        label="分级资产:"
        prop="dataSources"
      >
        <el-select
          ref="selectRef"
          class="width-full"
          key="dataSources"
          @focus="visibleChange"
          v-model="classForm.dataSources"
          multiple
          placeholder="请选择数据服务"
        >
          <el-option
            v-for="(item, key) in serverOrDatabseList"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="classForm.target == 'SCAN_TASK'"
        label="扫描任务:"
        prop="scanTask"
      >
        <el-select
          key="scanTask"
          class="width-full"
          filterable
          v-model="classForm.scanTask"
          placeholder="请选择扫描任务"
        >
          <el-option
            v-for="(item, key) in taskList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分级策略:" prop="type">
        <el-radio-group v-model="classForm.type" @change="changeStrategyVal">
          <el-radio
            v-for="item in classList"
            :label="item.value"
            :key="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
        <el-tooltip
          class="item"
          effect="dark"
          content=""
          placement="top"
          style="position: absolute; left: -26px; top: 8px"
        >
          <em class="el-icon-warning el-alert--info is-light"></em>
          <div
            slot="content"
            v-html="
              '正则分级是根据当前的标签规则对数据进行分类分级，AI分级是接入AI模型进行分类分级，混合模式是同时使用正则分级和AI分级，准确率更高，建议选择混合模式。'
            "
          ></div>
        </el-tooltip>
      </el-form-item>
      <template v-if="classForm.type == 'AI' || classForm.type == 'MIXED'">
        <el-form-item label="业务表策略:" prop="skipSystem">
          <el-checkbox v-model="classForm.skipSystem"
            >仅对业务表进行分类分级</el-checkbox
          >
        </el-form-item>
      </template>
      <el-form-item label="分级范围:" prop="increment">
        <el-radio-group v-model="classForm.increment">
          <el-radio :label="false">全量分级</el-radio>
          <el-radio :label="true">增量分级</el-radio>
        </el-radio-group>
        <el-tooltip
          class="item"
          effect="dark"
          content=""
          placement="top"
          style="position: absolute; left: -26px; top: 8px"
        >
          <em class="el-icon-warning el-alert--info is-light"></em>
          <div
            slot="content"
            v-html="'增量分级是对该服务没有进行过分类分级的数据进行分类分级。'"
          ></div>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="执行方式:" prop="executionType">
        <div class="border scan-executionType">
          <el-radio-group v-model="classForm.executionType" @change="oneChange">
            <el-radio label="IMMEDIATE">立即执行</el-radio>
            <el-radio label="TIME">定时执行</el-radio>
            <el-radio label="PERIOD">周期执行</el-radio>
          </el-radio-group>
          <div class="bg-gray" v-if="classForm.executionType != 'IMMEDIATE'">
            <el-form-item
              class="is-required"
              label="执行时间:"
              prop="startDate"
              label-width="90px"
              v-if="classForm.executionType == 'TIME'"
            >
              <el-date-picker
                v-model="classForm.startDate"
                type="datetime"
                value-format="timestamp"
                placeholder="选择日期时间"
                :picker-options="expireTimeOption"
              ></el-date-picker>
            </el-form-item>
            <el-form-item
              label="周期选择:"
              prop="period"
              label-width="90px"
              class="is-required"
              v-if="classForm.executionType == 'PERIOD'"
            >
              <el-select
                placeholder="频次"
                class="period-type-picker"
                size="small"
                v-model="classForm.period"
                @change="periodChange"
              >
                <el-option value="DAY" label="每日"></el-option>
                <el-option value="WEEK" label="每周"></el-option>
                <el-option value="MONTH" label="每月"></el-option>
              </el-select>
              <el-select
                placeholder="日期"
                class="period-type-picker"
                size="small"
                v-model="classForm.periodType"
                v-if="classForm.period != 'DAY'"
              >
                <template v-if="classForm.period == 'WEEK'">
                  <el-option value="1" label="周一"></el-option>
                  <el-option value="2" label="周二"></el-option>
                  <el-option value="3" label="周三"></el-option>
                  <el-option value="4" label="周四"></el-option>
                  <el-option value="5" label="周五"></el-option>
                  <el-option value="6" label="周六"></el-option>
                  <el-option value="7" label="周日"></el-option>
                </template>
                <template v-if="classForm.period == 'MONTH'">
                  <el-option
                    :value="item.value"
                    :label="item.label"
                    v-for="item in MONTH_LIST"
                    :key="item.value"
                  ></el-option>
                </template>
              </el-select>
              <el-time-picker
                v-else
                class="time-increment-picker"
                v-model="classForm.periodStart"
                placeholder="任意时间点"
                format="HH:mm"
                value-format="timestamp"
              ></el-time-picker>
            </el-form-item>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="覆盖策略:" prop="coveyLock">
        <el-checkbox v-model="classForm.coveyLock">覆盖锁定数据</el-checkbox>
      </el-form-item>
    </el-form>
    <div class="text-right" v-if="!isDetail">
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" :disabled="loading" @click="sure"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
import { deepCopy } from '@/utils/deep-copy';
import { handleCron, timestampToUTC8 } from '@/utils/string-utils';
import { getSystemTemplate } from '@/utils/storage-utils';
import {
  postAddClassData,
  postClassDetail,
  postTaskList,
  postAiList
} from '@/service/class-level-service';
import { postSourceList } from '@/service/data-sance-service';
export default {
  props: ['props'],
  data() {
    return {
      classForm: {
        dataSources: [],
        name: '',
        description: '',
        startDate: '',
        periodStart: '',
        periodType: '',
        executionType: 'IMMEDIATE',
        period: '',
        target: 'DATA_SOURCE',
        coveyLock: false,
        type: 'REGEX',
        increment: false,
        skipSystem: false
      },
      serverOrDatabseList: [],
      taskList: [],
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        target: [
          { required: true, message: '请选择分级对象', trigger: 'change' }
        ],
        dataSources: [
          { required: true, message: '请选择分级资产', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择分级策略', trigger: 'change' }
        ],
        increment: [
          { required: true, message: '请选择分级范围', trigger: 'change' }
        ],
        executionType: [
          { required: true, message: '请选择执行方式', trigger: 'change' }
        ],
        scanTask: [
          { required: true, message: '请选择扫描任务', trigger: 'change' }
        ]
      },
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        }
      },
      detailId: '',
      isCopy: false,
      classObjList: [
        { label: '数据服务', value: 'DATA_SOURCE' },
        { label: '资产扫描任务', value: 'SCAN_TASK' },
        { label: '全部资产', value: 'ALL' }
      ],
      classList: [
        { label: '正则分级', value: 'REGEX' },
        { label: 'AI分级', value: 'AI' },
        { label: '混合模式', value: 'MIXED' }
      ],
      isDetail: false,
      loading: false,
      templateName: ''
    };
  },

  components: {},

  async created() {
    const taskParams = {
      isPageQuery: true,
      page: 1,
      limit: 10000,
      columnList: ['name', 'id']
    };
    const taskRes = await postTaskList(taskParams);
    this.taskList = taskRes?.data?.rows || [];
    const { name, id, sid, class_obj_target } = this.props?.data || {};
    if (name && class_obj_target && sid) {
      this.classForm.name = name;
      this.classForm.target = class_obj_target;
      this.classForm.scanTask = sid;
    }
    if (!id) return;
    this.detailId = id;
    if (this.props?.data?.draw_type == 'copy') {
      this.isCopy = true;
    }
    if (this.props?.data?.draw_type == 'detail') {
      this.isDetail = true;
    }
    const params = {
      searchConditionList: [
        { fieldName: 'id', columnExp: '=', value: this.detailId }
      ],
      columnList: [
        'skip_system',
        'start_date',
        'id',
        'name',
        'description',
        'cron',
        'execution_type',
        'target',
        'scan_task_id',
        'type',
        'ai_model_id',
        'increment',
        'data_sources',
        'covey_lock'
      ]
    };
    const res = await postClassDetail(params);
    const resData = res.data;
    //这个需要判断是不是复制
    if (this.isCopy) {
      this.classForm.name = `副本-${resData.name}`;
    } else {
      this.classForm.name = resData.name;
    }

    this.classForm.description = resData.description;
    this.classForm.target = this.classObjList[resData.target].value;
    if (this.classForm.target == 'DATA_SOURCE') {
      this.updateData(JSON.parse(resData?.data_sources));
    } else if (this.classForm.target == 'SCAN_TASK') {
      this.classForm.scanTask = resData.scan_task_id;
    }
    this.classForm.type = this.classList[resData.type].value;
    if (this.classForm.type == 'AI' || this.classForm.type == 'MIXED') {
      this.classForm.skipSystem = resData.skip_system || false;
    }
    this.classForm.increment = resData.increment || false;
    this.classForm.coveyLock = resData.covey_lock;
    if (resData.execution_type == 1) {
      this.classForm.executionType = 'TIME';
      this.classForm.startDate = '';
    } else if (resData.execution_type == 2) {
      this.classForm.executionType = 'PERIOD';
      this.handleCronToTime(resData.cron);
    } else {
      this.classForm.executionType = 'IMMEDIATE';
    }
  },

  mounted() {
    this.templateName = getSystemTemplate()?.temName || '';
  },

  methods: {
    changeStrategyVal(val) {
      this.classForm.skipSystem = false;
    },
    async updateData(dataSources) {
      for (const item of dataSources) {
        try {
          const p = {
            isPageQuery: true,
            page: 1,
            searchConditionList: [
              {
                value: item.id,
                fieldName: 'id',
                columnExp: '='
              }
            ],
            limit: 10,
            columnList: [
              'name',
              'id',
              'url',
              'business_system',
              'connect_type',
              'port',
              'host',
              'username'
            ]
          };
          const res = await postSourceList(p);
          const resData = res?.data?.rows[0] || {};
          const { url } = resData;
          const itemInfo = {
            id: item.id,
            label:
              item?.databases?.length > 0
                ? `${url}(${item?.databases?.map((dItem) => dItem.name)?.join()})`
                : url,
            children: item?.databases
              ? item?.databases?.map((dItem) => dItem.name)
              : []
          };
          this.serverOrDatabseList.push(itemInfo);
        } catch (error) {
          console.error('请求数据时出错:', error);
        }
      }
      this.classForm.dataSources = this.serverOrDatabseList.map(
        (item) => item.id
      );
    },
    handleCronToTime(cron) {
      if (!cron) return;
      const corns = cron.split(' ');
      //每周周几
      if (cron.startsWith('0 0 0 ? *')) {
        const weekDay = corns[5];
        this.classForm.period = 'WEEK';
        this.classForm.periodType = (weekDay - 1).toString();
      } else if (cron.endsWith('* ? *')) {
        //每月几号
        const monthDay = corns[3];
        const day = monthDay == 'L' ? 'L' : monthDay;
        this.classForm.period = 'MONTH';
        this.classForm.periodType = day;
      } else {
        const h = corns[2];
        const m = corns[1];
        const s = corns[0];
        const date = new Date();
        const year = date.getFullYear();
        const month = date.getMonth();
        const day = date.getDay();
        const fixDay = `${year}-${month}-${day} ${h}:${m}:${s}`;
        const timestamp = new Date(fixDay).valueOf();
        this.classForm.period = 'DAY';
        this.classForm.periodStart = timestamp;
      }
    },
    changeVal(val) {
      this.$set(this.classForm, 'dataSources', []);
      this.serverOrDatabseList = [];
      this.$set(this.classForm, 'scanTask', '');
    },
    cancle() {
      this.$emit('cancel');
    },
    visibleChange(event) {
      //立即执行失去焦点的逻辑，防止一直触发
      this.$refs.selectRef.blur();
      this.$DrawAlert({
        params: {
          from: 'class',
          list: this.serverOrDatabseList,
          callBack: (res) => {
            const copyServerTableList = deepCopy(this.serverOrDatabseList);
            res.forEach((item) => {
              const index = copyServerTableList.findIndex(
                (citem) => citem.id == item.id
              );
              if (index != -1) {
                copyServerTableList[index] = item;
              } else {
                copyServerTableList.push(item);
              }
            });
            this.serverOrDatabseList = copyServerTableList.map((item) => {
              let labelStr = '';
              if (item?.children?.length > 0) {
                if (item.children.length == 1) {
                  labelStr = `${item.url}(${item.children[0]})`;
                } else {
                  labelStr = `${item.url}(${item.children[0]}...)`;
                }
              } else {
                labelStr = item.url;
              }
              return {
                label: labelStr,
                value: item.id,
                ...item
              };
            });
            this.classForm.dataSources = this.serverOrDatabseList.map(
              (item) => item.id
            );
          }
        },
        title: '添加分级资产',
        width: 70,
        componentObj: {
          component: () => import('../asset-sance/instance-select.vue')
        }
      });
    },
    sure() {
      this.$refs.classForm.validate((v) => {
        if (v) {
          this.loading = true;
          const copyForm = deepCopy(this.classForm);
          const p = {
            name: copyForm.name,
            description: copyForm.description,
            type: copyForm.type,
            executionType: copyForm.executionType,
            coveyLock: copyForm.coveyLock,
            target: copyForm.target,
            increment: copyForm.increment
          };
          if (this.serverOrDatabseList.length > 0) {
            p.dataSources = this.serverOrDatabseList.map((item) => {
              return {
                id: item.id,
                databases:
                  item?.children?.map((item) => {
                    return { name: item };
                  }) || []
              };
            });
          }
          if (copyForm.type == 'AI' || copyForm.type == 'MIXED') {
            p.skipSystem = copyForm.skipSystem;
          }
          if (copyForm.target == 'SCAN_TASK') {
            p.scanTask = {
              id: copyForm.scanTask
            };
          }

          if (copyForm.executionType == 'TIME') {
            p.startDate = timestampToUTC8(copyForm.startDate);
          }
          if (copyForm.executionType == 'PERIOD') {
            const timeCycl = {
              cycl: copyForm.period,
              date: copyForm.periodStart,
              week: copyForm.periodType,
              month: copyForm.periodType
            };
            p.cron = handleCron(timeCycl);
          }
          if (this.detailId && !this.isCopy) {
            p.id = this.detailId;
          }
          postAddClassData(p)
            .then((res) => {
              this.$message.success('操作成功');
              this.loading = false;
              this.$emit('save');
            })
            .catch((err) => {
              this.$message.error(err.msg || '操作失败');
              this.loading = false;
            });
        }
      });
    },
    oneChange() {
      this.classForm.startDate = '';
      this.classForm.period = '';
      this.classForm.periodStart = '';
      this.classForm.periodType = '';
    },
    periodChange() {
      this.classForm.periodType = '';
      this.classForm.periodStart = '';
    }
  }
};
</script>
<style lang="less" scoped>
.class-level {
  padding: 20px;
  .tip-context {
    padding: 5px;
    background: #db6a15;
    color: #fff;
    border-radius: 4px;
    font-size: 12px;
  }
  .scan-executionType {
    cursor: pointer;
    font-size: 14px;
    .qz-iconfont {
      font-size: 11px;
      margin-left: 2px;
    }
  }
  .scan-executionType {
    .scan-executionType-item {
      display: flex;
      justify-content: space-between;
      padding: 20px 0 0;
      > .el-form-item {
        flex: 2;
        &:last-child {
          flex: 3;
        }
      }
    }
  }
}
</style>
