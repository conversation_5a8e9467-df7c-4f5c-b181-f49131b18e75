<template>
  <div class="role">
    <el-form
      ref="form"
      :model="formInfo"
      :rules="rules"
      :disabled="formInfo.type === ROLE_TYPE_INTERNAL"
      size="small"
      label-width="100px"
    >
      <el-form-item prop="roleName" label="角色名称">
        <el-input
          v-model="formInfo.roleName"
          placeholder="请输入角色名称"
        ></el-input>
      </el-form-item>
      <el-form-item prop="remark" label="角色描述">
        <el-input
          v-model="formInfo.remark"
          placeholder="请输入角色描述"
        ></el-input>
      </el-form-item>
      <el-form-item prop="menuPermissions" label="菜单权限">
        <el-cascader
          v-model="formInfo.menuPermissions"
          :options="menuData"
          :props="{ value: 'id', label: 'name', multiple: true }"
          placeholder="请选择此角色的菜单权限"
          class="full-width"
          filterable
          collapse-tags
        >
        </el-cascader>
      </el-form-item>
      <!-- <el-form-item prop="opeartionPermission" label="操作权限">
        <template #label>
          <span>操作权限</span>
          <el-tooltip
            effect="dark"
            content="只读权限仅允许此角色下的账号查看具体的页面内容，不允许对页面内容进行操作！"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <el-select
          v-model="formInfo.opeartionPermission"
          placeholder="请选择此角色的操作权限"
          class="full-width"
        >
          <el-option
            :value="OPERATION_PERMISSION_READ"
            label="只读"
          ></el-option>
          <el-option
            :value="OPERATION_PERMISSION_WRITE"
            label="管理"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="dataPermission" label="数据权限">
        <template #label>
          <span>数据权限</span>
          <el-tooltip
            effect="dark"
            content="脱敏查看仅允许此角色下的账号查看脱敏后的数据内容，不允许对数据进行取消脱敏查看！"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <el-select
          v-model="formInfo.dataPermission"
          placeholder="请选择此角色的数据权限"
          class="full-width"
        >
          <el-option
            :value="DATA_PERMISSION_DESENSITIZE"
            label="脱敏查看"
          ></el-option>
          <el-option
            :value="DATA_PERMISSION_CANCEL_DESENS"
            label="原文查看"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="status" label="角色状态">
        <el-switch
          v-model="formInfo.status"
          :active-value="1"
          :inactive-value="0"
        ></el-switch>
      </el-form-item> -->
    </el-form>
    <div class="footer align-right">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button
        v-if="formInfo.type !== ROLE_TYPE_INTERNAL"
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="save"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import {
  DATA_PERMISSION_CANCEL_DESENS,
  DATA_PERMISSION_DESENSITIZE,
  OPERATION_PERMISSION_READ,
  OPERATION_PERMISSION_WRITE,
  ROLE_TYPE_CUSTOM,
  ROLE_TYPE_INTERNAL
} from '@/constant/common-constants';
import { getConfigableMenuList } from '@/router/menus';
import { cloneDeep } from 'lodash-es';

const menuData = [{ id: 'all', name: '全部' }, ...getConfigableMenuList()];

function getDefaultFormInfo() {
  return {
    id: '',
    roleName: '',
    remark: '',
    type: ROLE_TYPE_CUSTOM,
    status: 1,
    menuPermissions: [],
    opeartionPermission: OPERATION_PERMISSION_READ,
    dataPermission: DATA_PERMISSION_DESENSITIZE
  };
}

export default {
  props: ['params'],
  data() {
    return {
      ROLE_TYPE_INTERNAL,
      DATA_PERMISSION_DESENSITIZE,
      DATA_PERMISSION_CANCEL_DESENS,
      OPERATION_PERMISSION_READ,
      OPERATION_PERMISSION_WRITE,
      formInfo: this.getMergedFormInfo(),
      rules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ],
        dataPermission: [
          { required: true, message: '请选择此角色的数据权限', trigger: 'blur' }
        ],
        opeartionPermission: [
          { required: true, message: '请选择此角色的操作权限', trigger: 'blur' }
        ],
        menuPermissions: [
          { required: true, message: '请选择此角色的菜单权限', trigger: 'blur' }
        ]
      },
      saveLoading: false,
      menuData
    };
  },
  mounted() {
    console.log('roleDetail', this.params);
  },
  methods: {
    getCascade(menuId) {
      // 递归遍历menuList，当叶子节点为menuId时，响应路径
      const findPath = (options, menuId, path = []) => {
        for (const option of options) {
          if (option.id === menuId) {
            return [...path, option.id];
          }
          if (option.children && option.children.length > 0) {
            const result = findPath(option.children, menuId, [
              ...path,
              option.id
            ]);
            if (result) {
              return result;
            }
          }
        }
        return null;
      };
      return findPath(menuData, menuId) || [];
    },
    getMergedFormInfo() {
      const mergedFormInfo = Object.assign(
        getDefaultFormInfo(),
        this.params.detail
      );
      mergedFormInfo.menuPermissions = (
        mergedFormInfo.menuConditions?.[0]?.value || []
      ).map(this.getCascade);
      mergedFormInfo.opeartionPermission =
        mergedFormInfo.actionConditions?.[0]?.value?.[0] || 'READ';
      mergedFormInfo.dataPermission =
        mergedFormInfo.funcConditions?.[0]?.value?.[0] ||
        DATA_PERMISSION_DESENSITIZE;
      return mergedFormInfo;
    },
    cancel() {
      this.params.close();
      this.params.closeOutDrawer();
    },
    save() {
      console.log(this.formInfo);
      this.params.closeOutDrawer();
    }
  }
};
</script>

<style lang="less" scoped>
.role {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form {
    flex: auto;
    width: 100%;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
    padding-bottom: 0;
  }
  .footer {
    flex: none;
    padding: 10px;
    border-top: 1px solid @border-base-color;
  }
}
</style>
