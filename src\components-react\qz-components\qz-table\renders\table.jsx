import {
  createObject,
  evalExpression,
  isEffectiveApi,
  qsstringify,
  registerRenderer,
  ScopedContext,
  unRegister<PERSON>enderer,
  registerAction,
  getTreeAncestors
} from 'amis-core';
import { Tag } from 'amis-ui';
import CRUD2 from 'amis/lib/renderers/CRUD2';
import { cloneDeep } from 'lodash-es';
import { isObject } from 'lodash-es';
import { omitBy } from 'lodash-es';
// eslint-disable-next-line no-unused-vars
import React from 'react';
import { QzTableStore } from '../store/table-store';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components-react/popover';
import '../styles/table.less';
import moment from 'moment';
import { noUndefined } from './filter-form';

const INNER_EVENTS = [
  'selectedChange',
  'columnSort',
  'columnFilter',
  'columnSearch',
  'columnToggled',
  'orderChange',
  'rowClick',
  'rowDbClick',
  'rowMouseEnter',
  'rowMouseLeave',
  'selected'
];

class QzTableRenderer extends CRUD2 {
  static contextType = ScopedContext;

  static defaultProps = {
    toolbarInline: true,
    syncLocation: false,
    hideQuickSaveBtn: false,
    autoJumpToTopOnPagerChange: true,
    silentPolling: false,
    autoFillHeight: false,
    showSelection: true,
    primaryField: 'id',
    parsePrimitiveQuery: true
  };

  constructor(props, context) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);

    this.tableRef = null;
    this.splitterRef = null;
    this.viewRef = null;
    this.isDragging = false;
    this.startX = 0;
    this.startWidth = 0;
    this.maxViewWidth = props.maxViewWidth || 500; // 默认最大宽度500px
    this.minViewWidth = props.minViewWidth || 200; // 默认最小宽度200px
    this.lastViewWidth = 300; // 记录上次的视图宽度
    this.state = {
      isViewCollapsed: false // 视图是否收起
    };

    this.formItemCompCache = new Map(); // 添加组件缓存

    this.clearAllTags = this.clearAllTags.bind(this);
  }

  componentDidMount() {
    super.componentDidMount && super.componentDidMount();
    this.autoFillHeight();
    window.addEventListener('resize', this.autoFillHeight.bind(this));
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate && super.componentDidUpdate(prevProps);

    // 更新最大宽度设置
    if (prevProps.maxViewWidth !== this.props.maxViewWidth) {
      this.maxViewWidth = this.props.maxViewWidth || 500;
    }
    // 更新最小宽度设置
    if (prevProps.minViewWidth !== this.props.minViewWidth) {
      this.minViewWidth = this.props.minViewWidth || 200;
    }
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    const scoped = this.context;
    scoped.unRegisterComponent(this);

    // 清理拖拽事件监听器
    this.removeDragListeners();
    window.removeEventListener('resize', this.autoFillHeight.bind(this));
  }

  autoFillHeight() {
    const scoped = this.context;
    const { id, autoFillHeight } = this.props;
    // Use setTimeout to ensure this runs after rendering is complete
    setTimeout(() => {
      if (autoFillHeight) {
        // 找到table2组件实例
        const table2Inst = scoped.getComponentById(id);
        table2Inst?.tableRef?.updateAutoFillHeight?.();
      }
    }, 0);
  }

  // 添加拖拽事件处理方法
  handleSplitterMouseDown = (e) => {
    // 如果视图已收起，不允许拖拽
    if (this.state.isViewCollapsed) {
      return;
    }

    e.preventDefault();
    this.isDragging = true;
    this.startX = e.clientX;
    this.startWidth = this.viewRef?.offsetWidth || 300;

    // 添加拖拽状态样式
    if (this.splitterRef) {
      this.splitterRef.classList.add('dragging');
    }

    this.addDragListeners();
  };

  handleSplitterMouseMove = (e) => {
    if (!this.isDragging) return;

    const deltaX = e.clientX - this.startX;
    const newWidth = Math.max(
      this.minViewWidth,
      Math.min(this.maxViewWidth, this.startWidth + deltaX)
    );

    if (this.viewRef) {
      this.viewRef.style.width = `${newWidth}px`;
      // 实时更新记录的宽度
      this.lastViewWidth = newWidth;
    }
  };

  handleSplitterMouseUp = () => {
    this.isDragging = false;

    // 移除拖拽状态样式
    if (this.splitterRef) {
      this.splitterRef.classList.remove('dragging');
    }

    this.removeDragListeners();
  };

  // 切换视图收起/展开状态
  toggleViewCollapse = (e) => {
    e.stopPropagation(); // 阻止事件冒泡到拖拽处理

    if (this.state.isViewCollapsed) {
      // 展开视图，恢复到之前的宽度
      this.expandView();
    } else {
      // 收起视图
      this.collapseView();
    }
  };

  collapseView = () => {
    if (this.viewRef) {
      // 记录当前宽度
      this.lastViewWidth = this.viewRef.offsetWidth;
      // 收起视图
      this.viewRef.style.width = '0px';
      // 设置视图为收起状态
      this.setState({ isViewCollapsed: true });

      // 更新分割线样式
      if (this.splitterRef) {
        this.splitterRef.classList.add('collapsed');
      }
    }
  };

  expandView = () => {
    if (this.viewRef) {
      // 恢复到之前记录的宽度
      this.viewRef.style.width = `${this.lastViewWidth}px`;
      // 设置视图为展开状态
      this.setState({ isViewCollapsed: false });

      // 移除收起状态样式
      if (this.splitterRef) {
        this.splitterRef.classList.remove('collapsed');
      }
    }
  };

  addDragListeners = () => {
    document.addEventListener('mousemove', this.handleSplitterMouseMove);
    document.addEventListener('mouseup', this.handleSplitterMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  };

  removeDragListeners = () => {
    document.removeEventListener('mousemove', this.handleSplitterMouseMove);
    document.removeEventListener('mouseup', this.handleSplitterMouseUp);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  };

  // 渲染收起/展开按钮
  renderToggleButton = () => {
    return (
      <div
        className="view-toggle-button"
        onClick={this.toggleViewCollapse}
        title={this.state.isViewCollapsed ? '展开视图' : '收起视图'}
      >
        <i
          className={`el-icon-${this.state.isViewCollapsed ? 'caret-right' : 'caret-left'}`}
        ></i>
      </div>
    );
  };

  async getData(
    /** 静默更新，不显示加载状态 */
    silent,
    /** 清空已选择数据 */
    clearSelection,
    /** 强制重新加载 */
    forceReload = false,
    /** 加载更多数据，默认模式取props中的配置，只有事件动作需要直接触发 */
    loadMore
  ) {
    const {
      store,
      api,
      messages,
      pageField,
      perPageField,
      interval,
      stopAutoRefreshWhen,
      silentPolling,
      syncResponse2Query,
      keepItemSelectionOnPageChange,
      stopAutoRefreshWhenModalIsOpen,
      pickerMode,
      loadType,
      loadDataOnce,
      source,
      columns,
      perPage
    } = this.props;

    // reload 需要清空用户选择
    if (
      !loadMore &&
      keepItemSelectionOnPageChange &&
      clearSelection &&
      !pickerMode
    ) {
      store.setSelectedItems([]);
      store.setUnSelectedItems([]);
    }

    clearTimeout(this.timer);
    this.lastQuery = store.query;
    const loadDataMode = loadMore ?? loadType === 'more';

    const data = createObject(store.data, store.query);
    // 如果有分组字段，则将分组字段添加到查询参数中
    // 注意：分组优先级高于筛选表单。分组和表单筛选同一个字段时，给接口的是分组值
    const { groupField, curGroup } = store;
    if (groupField && curGroup) {
      data[groupField.name] = curGroup;
    }

    // handleLoadMore 是在事件触发后才执行，首次加载并不走到 handleLoadMore
    // 所以加载更多模式下，首次加载也需要使用设置的 perPage，避免前后 perPage 不一致导致的问题
    if (loadDataMode && perPage) {
      store.changePerPage(perPage);
    }

    if (isEffectiveApi(api, data)) {
      const value = await store.fetchInitData(api, data, {
        successMessage: messages && messages.fetchSuccess,
        errorMessage: messages && messages.fetchFailed,
        autoAppend: true,
        forceReload,
        loadDataOnce,
        source,
        silent,
        pageField,
        perPageField,
        loadDataMode,
        syncResponse2Query,
        columns: store.columns ?? columns,
        isTable2: true
      });

      value?.ok && // 接口正常返回才继续轮训
        interval &&
        !this.stopingAutoRefresh &&
        this.mounted &&
        (!stopAutoRefreshWhen ||
          !(
            stopAutoRefreshWhen &&
            evalExpression(
              stopAutoRefreshWhen,
              createObject(store.data, store.query)
            )
          )) &&
        // 弹窗期间不进行刷新
        (!stopAutoRefreshWhenModalIsOpen ||
          (!store.dialogOpen && !store?.parentStore?.dialogOpen)) &&
        (this.timer = setTimeout(
          this.getData.bind(this, silentPolling, undefined, true),
          Math.max(interval, 1000)
        ));
    } else if (source) {
      store.initFromScope(data, source, {
        columns: store.columns ?? columns
      });
    }

    return store.data;
  }

  async reload(subpath, query, ctx) {
    if (query) {
      this.props.store.bulkChangeFilterFormData(query);
    }

    const scoped = this.context;
    if (subpath) {
      return scoped.reload(
        query ? `${subpath}?${qsstringify(query)}` : subpath,
        ctx
      );
    }

    return super.reload(subpath, query);
  }

  async receive(values, subPath) {
    const scoped = this.context;
    if (subPath) {
      return scoped.send(subPath, values);
    }

    return super.receive(values);
  }

  reloadTarget(target, data) {
    const scoped = this.context;
    scoped.reload(target, data);
  }

  closeTarget(target) {
    const scoped = this.context;
    scoped.close(target);
  }

  renderFilter(filterSchema) {
    this.autoFillHeight();

    if (
      !filterSchema ||
      (Array.isArray(filterSchema) && filterSchema.length === 0)
    ) {
      return null;
    }

    const filterSchemas = Array.isArray(filterSchema)
      ? filterSchema
      : isObject(filterSchema) && filterSchema.type != null
        ? [filterSchema]
        : [];

    if (filterSchemas.length < 1) {
      return null;
    }

    const {
      render,
      store,
      testIdBuilder,
      classnames: cx,
      $$editor
    } = this.props;
    const filterFormVisible = store.filterFormVisible;

    return (
      <div
        className={cx('Crud2-filter')}
        {...testIdBuilder?.getChild('filter').getTestId()}
        style={
          filterFormVisible || $$editor
            ? { display: 'block' }
            : {
                display: 'none',
                height: 0,
                overflow: 'hidden',
                marginBottom: 0
              }
        }
      >
        {filterSchemas.map((item, index) =>
          render(
            `filter/${index}`,
            {
              ...item,
              type: 'crud-filter-form'
            },
            {
              key: index + 'filter',
              store,
              onSubmit: (data) => {
                store.toggleFilterForm();
                this.handleSearch({ query: data, resetPage: true });
              },
              onReset: (data) => {
                this.resetQuery(data);
              }
            }
          )
        )}
      </div>
    );
  }

  resetQuery(data) {
    const resetQueries = {};
    Object.keys(data).forEach((key) => (resetQueries[key] = ''));

    this.handleSearch({
      query: resetQueries,
      resetQuery: true,
      replaceQuery: true,
      resetPage: true
    });
  }

  handleQueryTagClose(key) {
    const { store } = this.props;
    const resetQueries = cloneDeep(store.filterData || {});
    if (Object.keys(resetQueries).includes(key)) {
      delete resetQueries[key];
    }
    store.removeFilterTag(key);
    this.handleSearch({
      query: resetQueries,
      resetQuery: true,
      replaceQuery: true,
      resetPage: true
    });
  }

  getFilterItemTag(itemSchema) {
    const { store } = this.props;
    const scoped = this.context;
    const value = store.filterFormData[itemSchema.name];

    if (value === undefined || value === null || value === '') {
      return null;
    } else {
      const tag = {
        name: itemSchema.name,
        label: ''
      };

      // 根据组件类型处理标签的显示内容
      let comp = null;
      if (this.formItemCompCache.has(itemSchema.id)) {
        comp = this.formItemCompCache.get(itemSchema.id);
      } else {
        comp = scoped.getComponentById(itemSchema.id);
        this.formItemCompCache.set(itemSchema.id, comp);
      }
      switch (itemSchema.type) {
        case 'switch':
          if (!value) return null; // 如果值为false，不显示标签
          tag.label = `${itemSchema.label}：是`;
          break;
        case 'crud-input-ignore-case':
          tag.label = `${itemSchema.label}：${value}${store.filterFormData[`${itemSchema.name}_ignoreCase`] ? '(忽略大小写)' : ''}`;
          break;
        case 'filter-form-select':
        case 'filter-form-radios':
        case 'filter-form-checkboxes':
        case 'filter-form-cascader':
          if (comp) {
            const labelField = itemSchema.labelField || 'label';
            const options = comp.props.options || [];
            const selectedOptions = comp.props.selectedOptions || [];
            const tagLabels = selectedOptions.map((option) => {
              const ancestors = getTreeAncestors(options, option, true);
              return ancestors
                .map((item) => item[labelField || 'label'])
                .join('/');
            });

            const { showOperator, operators } = itemSchema;
            const operatorValue =
              store.filterFormData[`${itemSchema.name}_operator`];
            const operatorLabel =
              operators?.find((op) => op.value === operatorValue)?.label || '';
            tag.label = `${itemSchema.label}${showOperator && operatorLabel ? `(${operatorLabel})` : ''}：${tagLabels.join(',')}`;
          } else {
            return null; // 如果组件不存在，直接返回
          }
          break;
        case 'input-date':
        case 'input-datetime':
        case 'input-time':
        case 'input-month':
        case 'input-quarter':
        case 'input-year':
        case 'input-date-range':
        case 'input-datetime-range':
        case 'input-time-range':
        case 'input-month-range':
        case 'input-quarter-range':
        case 'input-year-range':
          if (itemSchema) {
            const valueFormat = itemSchema.valueFormat || 'X';
            const displayFormat = itemSchema.displayFormat || 'YYYY-MM-DD';
            const values = Array.isArray(value) ? value : value.split(',');
            const formattedValues = values.map((val) =>
              moment(val, valueFormat).format(displayFormat)
            );
            tag.label = `${itemSchema.label}：${formattedValues.join(' ~~ ')}`;
          }
          break;
        default:
          // 对于其他类型的组件，直接使用label和value拼接
          tag.label = `${itemSchema.label}：${value}`;
          break;
      }
      return tag;
    }
  }

  renderQueryTags() {
    const { store, filter } = this.props;
    const filterFormVisible = store.filterFormVisible;
    if (filterFormVisible) {
      return null;
    }

    const tags = [];
    const filterItemList = filter?.body || [];
    const filterTags = filterItemList
      .map((item) => this.getFilterItemTag(item))
      .filter(Boolean);

    filterTags.forEach(({ name, label }) => {
      tags.push(
        <Tag
          label={label}
          displayMode="rounded"
          key={name}
          onClose={this.handleQueryTagClose.bind(this, name)}
          closable={true}
        />
      );
    });

    if (!tags.length) return null;

    return (
      <div className="filter-tag-container">
        {tags}
        <span className="action-link" onClick={this.clearAllTags}>
          清空
        </span>
      </div>
    );
  }

  clearAllTags() {
    const { store } = this.props;
    // 重置表单数据
    store.resetFilterFormData();
    this.resetQuery(noUndefined(cloneDeep(store.filterFormData)));
  }

  setCurGroup(group) {
    const { store } = this.props;
    store.setCurGroup(group);
    console.log(store.groupField, store.curGroup);
    this.handleSearch({ query: store.filterData, resetPage: true });
  }

  calcGroupMaxCnt(list, groupName = '') {
    let maxGroupCnt = 5;
    const apiTableWidth = this.tableRef?.clientWidth || 0;
    const groupNameWidth = 13 * groupName.length;

    const groupMaxWrapperWidth = apiTableWidth - groupNameWidth - 70;
    if (list.length) {
      const widthArr = [
        46 + 13 * (list[0].label.length + list[0].count.toString().length)
      ];
      for (let i = 1; i < list.length; i++) {
        widthArr.push(
          widthArr[i - 1] +
            46 +
            13 * (list[i].label.length + list[i].count.toString().length)
        );
      }
      if (widthArr[widthArr.length - 1] < groupMaxWrapperWidth) {
        maxGroupCnt = widthArr.length;
      } else {
        const countIndex = widthArr.findIndex((item) => {
          return item > groupMaxWrapperWidth;
        });
        maxGroupCnt = countIndex;
      }
    }

    return {
      visibleGroupResult: list.slice(0, maxGroupCnt),
      restGroupResult: list.slice(maxGroupCnt, list.length)
    };
  }

  renderGroupResult() {
    const { store } = this.props;
    const groupResult = store.groupResult || [];
    if (!groupResult || groupResult.length === 0) {
      return null;
    }

    const groupField = store.groupField || {};
    const { visibleGroupResult, restGroupResult } = this.calcGroupMaxCnt(
      groupResult || [],
      groupField.label
    );
    let curGroup = store.curGroup || '';

    // 如果当前没有选中分组结果，默认选中第一个，并检索表格数据
    if (!curGroup) {
      curGroup = groupResult[0].value;
      this.setCurGroup(groupResult[0].value);
    }

    return (
      <div className="ApiTable__group-results">
        <span className="ApiTable__group-name">
          按 {groupField.label} 分组：
        </span>
        <div className="ApiTable__group-list">
          {visibleGroupResult.map((item, index) => (
            <div
              key={index}
              className={[
                'ApiTable__group-option',
                curGroup === item.value ? ' selected' : ''
              ]
                .filter((item) => !!item)
                .join(' ')}
              onClick={() => this.setCurGroup(item.value)}
            >
              {item.label}（{item.count}）
            </div>
          ))}
        </div>
        {restGroupResult.length > 0 ? (
          <Popover className="ApiTable__group-more">
            <PopoverTrigger>
              <i className="el-icon-caret-bottom"></i>
            </PopoverTrigger>
            <PopoverContent className="ApiTable__group-more-popper">
              {restGroupResult.map((item, index) => (
                <div
                  key={index}
                  className={[
                    'api-table__group-more-option',
                    curGroup === item.value ? ' selected' : ''
                  ]
                    .filter((item) => !!item)
                    .join(' ')}
                  onClick={() => this.setCurGroup(item.value)}
                >
                  {item.label}（{item.count}）
                </div>
              ))}
            </PopoverContent>
          </Popover>
        ) : null}
        <div
          className="ApiTable__cancel-group"
          onClick={() => this.cancelGroup()}
        >
          取消分组
        </div>
      </div>
    );
  }

  cancelGroup() {
    const { store } = this.props;
    store.cancelGroup();
    console.log(store.groupField, store.curGroup);
    this.handleSearch({ query: store.filterData, resetPage: true });
  }

  renderToolbar(region, toolbar) {
    if (!toolbar) {
      return null;
    }

    toolbar = [].concat(toolbar);

    return toolbar.map((item, index) =>
      this.renderChild(`${region}/${index}`, item, {
        key: index + '',
        crudSchema: this.props.$schema
      })
    );
  }

  contentWithoutView() {
    const {
      columns,
      className,
      style,
      bodyClassName,
      filter: filterSchema,
      render,
      store,
      mode = 'table2',
      syncLocation,
      children,
      bulkActions,
      pickerMode,
      selectable,
      multiple,
      valueField,
      primaryField,
      value,
      hideQuickSaveBtn,
      itemActions,
      classnames: cx,
      keepItemSelectionOnPageChange,
      maxKeepItemSelectionLength,
      onEvent,
      onAction,
      popOverContainer,
      translate: __,
      onQuery,
      autoGenerateFilter,
      onSelect,
      autoFillHeight,
      showSelection,
      headerToolbar,
      footerToolbar,
      // columnsTogglable 在本渲染器中渲染，不需要 table 渲染，避免重复
      columnsTogglable,
      headerToolbarClassName,
      footerToolbarClassName,
      id,
      testIdBuilder,
      ...rest
    } = this.props;

    const body = render(
      'body',
      {
        ...rest,
        // 通用事件 例如cus-event 如果直接透传给table 则会被触发2次
        // 因此只将下层组件table、cards中自定义事件透传下去 否则通过crud配置了也不会执行
        onEvent: omitBy(onEvent, (event, key) => !INNER_EVENTS.includes(key)),
        type: mode,
        columns: mode.startsWith('table')
          ? store.columns || columns
          : undefined,
        id
      },
      {
        key: 'body',
        className: cx('Crud2-body', bodyClassName),
        ref: this.controlRef,
        autoGenerateFilter: !filterSchema && autoGenerateFilter,
        autoFillHeight: autoFillHeight,
        checkAll: false, // 不使用组件的全选，因为不在工具栏里
        selectable: !!(selectable ?? pickerMode),
        itemActions,
        multiple: multiple,
        // columnsTogglable在CRUD2中渲染 但需要给table2传columnsTogglable为false 否则列数超过5 table2会自动渲染
        columnsTogglable: false,
        selected:
          pickerMode || keepItemSelectionOnPageChange
            ? store.selectedItemsAsArray
            : undefined,
        keepItemSelectionOnPageChange,
        maxKeepItemSelectionLength,
        // valueField: valueField || primaryField,
        primaryField: primaryField,
        testIdBuilder,
        items: store.data.items,
        query: store.query,
        orderBy: store.query.orderBy,
        orderDir: store.query.orderDir,
        popOverContainer,
        onSave: this.handleSave.bind(this),
        onSaveOrder: this.handleSaveOrder,
        onSearch: this.handleQuerySearch,
        onSort: this.handleQuerySearch,
        onSelect: this.handleSelect,
        onAction: this.handleAction,
        data: store.mergedData,
        loading: store.loading,
        host: this
      }
    );

    return (
      <div
        ref={(el) => (this.tableRef = el)}
        className={cx('Crud2', className, {
          'is-loading': store.loading
        })}
        style={style}
        data-id={id}
        data-role="container"
        {...testIdBuilder?.getTestId()}
      >
        <div
          className={cx(
            'Crud2-toolbar',
            'Crud2-header-toolbar',
            headerToolbarClassName
          )}
        >
          {this.renderToolbar('headerToolbar', headerToolbar)}
        </div>
        {this.renderFilter(filterSchema)}
        {this.renderQueryTags()}
        {this.renderGroupResult()}
        {showSelection && keepItemSelectionOnPageChange && multiple !== false
          ? this.renderSelection()
          : null}
        {body}
        <div
          className={cx(
            'Crud2-toolbar',
            'Crud2-footer-toolbar',
            footerToolbarClassName
          )}
        >
          {this.renderToolbar('footerToolbar', footerToolbar)}
        </div>
      </div>
    );
  }

  renderView() {
    const { view, render } = this.props;
    if (view) {
      return render('view', view);
    }
  }

  // 重写组件渲染逻辑
  render() {
    const { showView } = this.props;

    return (
      <>
        {!showView ? (
          this.contentWithoutView()
        ) : (
          <div className="crud-with-view">
            <div
              ref={(el) => (this.viewRef = el)}
              className="crud-view-wrapper"
              style={{ width: '300px' }} // 默认宽度
            >
              {this.renderView()}
            </div>
            <div
              ref={(el) => (this.splitterRef = el)}
              className="view-splitter"
              onMouseDown={this.handleSplitterMouseDown}
            >
              {this.renderToggleButton()}
            </div>
            {this.contentWithoutView()}
          </div>
        )}
      </>
    );
  }
}

unRegisterRenderer('crud2');
registerRenderer({
  type: 'crud2',
  storeType: QzTableStore.name,
  isolateScope: true,
  component: QzTableRenderer
});

export class CustomSetvalueAction {
  async run(action, renderer, event) {
    const scoped = event.context.scoped;
    if (scoped?.rendererType === 'crud2') {
      // 处理 crud2 的特定逻辑
      const { store } = scoped.component.props;
      const { name, value } = action.args || {};
      store.changeValue(name, value, true, true);
    }
  }
}

registerAction('setCrud2Value', new CustomSetvalueAction());
