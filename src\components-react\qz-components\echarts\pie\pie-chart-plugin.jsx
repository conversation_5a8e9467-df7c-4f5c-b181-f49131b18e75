import { BasePlugin, getSchemaTpl } from 'amis-editor';
const rendererName = 'qz-pie-chart-render';
export class PieChartPlugin extends BasePlugin {
  // 这里要跟对应的渲染器名字对应上
  // 注册渲染器的时候会要求指定渲染器名字
  rendererName = rendererName;
  // 暂时只支持这个，配置后会开启代码编辑器
  $schema = '/schemas/UnknownSchema.json';
  //组件关键字，用来辅助组件列表搜索
  searchKeywords = '';
  // 用来配置名称和描述
  name = '环形图/饼图组件';
  title = '';
  description = '环形图/饼图组件';

  // tag，决定会在哪个 tab 下面显示的
  tags = ['全知组件'];

  // 图标
  icon = 'fa fa-user';

  // 用来生成预览图的
  previewSchema = {
    type: rendererName
  };

  // 拖入组件里面时的初始数据
  scaffold = {
    type: rendererName,
    title: '',
    echartSql: '',
    echartData: ''
  };

  // 右侧面板相关
  panelBodyCreator = (context) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: [
          getSchemaTpl('layout:originPosition', { value: 'left-top' }),
          getSchemaTpl('description', {
            name: 'echartSql',
            label: 'echartSql内容',
            maxRows: 10,
            rows: 10,
            placeholder: '请输入echartSql内容'
          }),
          getSchemaTpl('description', {
            name: 'title',
            label: '环形/饼图标题',
            maxRows: 10,
            rows: 10,
            placeholder: '标题'
          }),
          getSchemaTpl('description', {
            name: 'echartData',
            label: '数据值',
            maxRows: 10,
            rows: 10,
            placeholder: '可以为空,用于获取后端接口返回的echart数据'
          })
        ]
      }
    ]);
  };
}
