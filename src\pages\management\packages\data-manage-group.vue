<template>
  <div class="data-manage-group">
    <el-form
      ref="form"
      :model="formInfo"
      :rules="rules"
      :disabled="formInfo.type === 1"
      size="small"
      label-width="100px"
    >
      <el-form-item prop="groupName" label="分组名称">
        <el-input
          v-model="formInfo.name"
          placeholder="请输入分组名称"
        ></el-input>
      </el-form-item>
      <el-form-item prop="description" label="分组描述">
        <el-input
          v-model="formInfo.description"
          placeholder="请输入分组描述"
        ></el-input>
      </el-form-item>
       <el-form-item prop="assetPermission" label="资产权限">
    <div class="asset-permission-container">
      <div 
        v-for="(permission, index) in formInfo.assetPermission" 
        :key="index"
        class="permission-row"
      >
        <span class="permission-type-label">按服务分配</span>
        
        <el-select
          v-model="permission.assetType"
          placeholder="请选择资产类型"
          class="asset-type-select"
          @change="handleAssetTypeChange(index)"
        >
          <el-option label="服务名称" value="SERVER_NAME"></el-option>
          <el-option label="服务类型" value="SERVER_TYPE"></el-option>
          <el-option label="业务系统" value="SERVER_SYSTEM"></el-option>
        </el-select>
        
        <el-select
          v-model="permission.assetValues"
          placeholder="请选择具体值"
          class="asset-values-select"
          multiple
          filterable
          clearable
          collapse-tags
          :loading="assetValuesLoading[index]"
        >
          <el-option
            v-for="item in assetValueOptions[index]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        
        <el-button
          v-if="formInfo.assetPermission.length > 1"
          type="text"
          icon="el-icon-delete"
          @click="removePermission(index)"
          class="delete-btn"
        >
          删除
        </el-button>
      </div>
      
      <el-button
        type="primary"
        @click="addPermission"
        class="add-condition-btn"
        style="width: 100%;margin-top: 10px;"
      >
        添加条件
      </el-button>
    </div>
  </el-form-item>
    </el-form>
    <div class="footer align-right">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button
        v-if="formInfo.type !== 1"
        :loading="saveLoading"
        type="primary"
        size="small"
        @click="save"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import {
postDataManageSave
} from '@/service/data-manage-service';
import { saveDataGroup } from '@/service/management-service';
import { getNodeList } from '@/service/multi-node-service';
import { getNetworkSegmentList } from '@/service/network-segment-service';
import {
  getAppList,
  getImportedDepartmentList
} from '@/service/webapp-service';
import { cloneDeep } from 'lodash-es';

export default {
  props: ['params'],
  data() {
  return {
    formInfo: {
      id: '',
      groupName: '',
      groupDesc: '',
      userIds: [],
      assetPermission: [
        {
          assetType: '',
          assetValues: []
        }
      ],
      creator: 0,
      modifier: 0
    },
    rules: {
      groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
      assetPermission: [
        {
          required: true,
          message: '请配置资产权限',
          validator: this.validateAssetPermission,
          trigger: 'blur'
        }
      ]
    },
    // 资产值选项数据
    assetValueOptions: [[]],
    // 资产值加载状态
    assetValuesLoading: [false],
    saveLoading: false
  };
},
  created() {
    // // 获取服务名称
    // getImportedDepartmentList().then((res) => {
    //   this.departmentList = (res.data || []).map((item) => ({
    //     id: item,
    //     name: item
    //   }));
    // });

    // // 获取服务类型
    // getNetworkSegmentList({
    //   page: 1,
    //   limit: 1000,
    //   needInternet: true
    // }).then((res) => {
    //   this.domainList = res.data?.rows || [];
    // });

    // // 获取业务系统
    // getNodeList({ page: 1, limit: 1000 }).then((res) => {
    //   this.nodeList = res.data?.rows || [];
    // });
  },
  mounted(){
    if(this.params.detail.id){
        this.loadExistingAssetValues();
    }
  },
methods: {
  // 切换数据源
  handleAssetTypeChange(index) {
    const permission = this.formInfo.assetPermission[index];
    permission.assetValues = [];
    
    if (!permission.assetType) {
      this.$set(this.assetValueOptions, index, []);
      return;
    }
    
    // 根据资产类型切换对应的数据源
    let dataSource = [];
    switch (permission.assetType) {
      case 'SERVER_NAME':
        dataSource = this.serverNameList;
        break;
      case 'SERVER_TYPE':
        dataSource = this.serverTypeList;
        break;
      case 'SERVER_SYSTEM':
        dataSource = this.serverSystemList;
        break;
      default:
        dataSource = [];
    }
    
    const options = dataSource.map(item => ({
      label: item.name || item,
      value: item.id || item.value || item
    }));
    
    this.$set(this.assetValueOptions, index, options);
  },
  
  addPermission() {
    this.formInfo.assetPermission.push({
      assetType: '',
      assetValues: []
    });
    this.assetValueOptions.push([]);
  },
  
  removePermission(index) {
    this.formInfo.assetPermission.splice(index, 1);
    this.assetValueOptions.splice(index, 1);
  },
  
  validateAssetPermission(rule, value, callback) {
    if (!value || value.length === 0) {
      callback(new Error('请至少配置一个资产权限'));
      return;
    }
    
    for (let i = 0; i < value.length; i++) {
      const permission = value[i];
      if (!permission.assetType) {
        callback(new Error(`第${i + 1}个权限条件的资产类型不能为空`));
        return;
      }
      if (!permission.assetValues || permission.assetValues.length === 0) {
        callback(new Error(`第${i + 1}个权限条件的具体值不能为空`));
        return;
      }
    }
    
    callback();
  },
  
  loadExistingAssetValues() {
    for (let i = 0; i < this.formInfo.assetPermission.length; i++) {
      const permission = this.formInfo.assetPermission[i];
      if (permission.assetType) {
        this.handleAssetTypeChange(i);
      }
    }
  },
  
  // 保存方法
  save() {
    this.$refs.form.validate((valid) => {
      if (valid) {
        this.saveLoading = true;
        
        const params = {
          ...this.formInfo
        };
        
        // 调用保存接口
        postDataManageAdd(params)
          .then(() => {
            this.$message.success('保存成功');
            this.params.callback && this.params.callback();
            this.params.close();
          })
          .catch((error) => {
            this.$message.error('保存失败');
            console.error('保存失败:', error);
          })
          .finally(() => {
            this.saveLoading = false;
          });
      }
    });
  },
  
  // 取消
  cancel() {
    this.params.close();
  }
}
};
</script>

<style lang="less" scoped>

</style>
