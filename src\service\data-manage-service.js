import {
  DATA_URL_DATA_MANAGE_SAVE,
  DATA_URL_DATA_MANAGE_LIST,
  DATA_URL_DATA_MANAGE_DELETE,
  DATA_URL_DATA_MANAGE_ASSIGN_ACCOUNT,
  DATA_URL_DATA_MANAGE_BATCH_DELETE,
  DATA_URL_DATA_MANAGE_BATCH_ASSIGN_ACCOUNT
} from '@/constant/data-url-constants';
import { doGet, doPost } from '@quanzhiFE/qz-frontend';

export const postDataManageSave = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_MANAGE_SAVE,
      params
    },
    true
  );
};

export const postDataManageList = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_MANAGE_LIST,
      params
    },
    true
  );
};

export const postDataManageDelete = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_MANAGE_DELETE,
      params
    },
    true
  );
};

export const postDataManageAssignAccount = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_MANAGE_ASSIGN_ACCOUNT,
      params
    },
    true
  );
};

export const postDataManageBatchDelete = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_MANAGE_BATCH_DELETE,
      params
    },
    true
  );
};

export const postDataManageBatchAssignAccount = (params) => {
  return doPost(
    {
      url: DATA_URL_DATA_MANAGE_BATCH_ASSIGN_ACCOUNT,
      params
    },
    true
  );
};
