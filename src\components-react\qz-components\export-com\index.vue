<template>
  <div class="export-com">
    <div class="flex">
      <div class="upload-title">导入文件：</div>
      <el-upload
        ref="uploadFile"
        size="small"
        :action="DATA_URL_FILE_UPLOAD"
        :on-change="handleChange"
        :on-success="handleSuccess"
        :file-list="fileList"
        :limit="1"
        :auto-upload="false"
      >
        <el-button size="small" type="primary">点击上传</el-button>
        <template
          v-if="
            dialogType == 'asset' ||
            dialogType == 'serve' ||
            dialogType == 'label'
          "
        >
          <div slot="tip" class="el-upload__tip">
            支持扩展名xlsx,xls(文件大小不超过500M),可点击
            <a @click="downloadFile">下载模版</a>
          </div>
        </template>
        <template v-else-if="dialogType == 'assetDesc'">
          <div slot="tip" class="el-upload__tip">
            支持扩展名xlsx,xls(文件大小不超过500M),可点击
            <a @click="downloadFile">下载模版</a>
            ,将当前列表筛选展示的所有数据进行导出，您可以在此基础上修改字段备注再上传，请注意，只允许修改备注，请勿增删改其他列的内容。
            <div class="tip-context" v-if="isMore">
              当列表数量超过1万，数据量过多，可能速度较慢，可以先在列表筛选，再下载模版。
            </div>
          </div>
        </template>
        <template v-else-if="dialogType == 'assetClass'">
          <div slot="tip" class="el-upload__tip">
            支持扩展名xlsx,xls(文件大小不超过500M),可点击
            <a @click="downloadFile">下载模版</a>
            ,将当前列表筛选展示的所有数据进行导出，您可以在此基础上修改字段分类再上传，请注意，只允许修改分类，请勿增删改其他列的内容。
            <div class="tip-context" v-if="isMore">
              当列表数量超过1万，数据量过多，可能速度较慢，可以先在列表筛选，再下载模版。
            </div>
          </div>
        </template>
        <!-- <template v-else-if="dialogType=='serve'">
            <div slot="tip" class="el-upload__tip">支持扩展名xlsx,xls(文件大小不超过500M),可点击
                <a @click="downloadFile">下载模版</a>
            </div>
        </template> -->
        <!-- <template v-else-if="dialogType=='label'">
            <div slot="tip" class="el-upload__tip">支持扩展名xlsx,xls(文件大小不超过500M),可点击<a @click="downloadFile">下载模版</a>
                <div class="success_tip"><qz-icon class="icon-success"></qz-icon>有13条数据符合规范可以导入</div>
                <div class="error_tip"><qz-icon class="icon-shibai"></qz-icon>有3条数据不符合规范无法导入<a @click="downloadErrorFile">点击下载</a></div>
            </div>
        </template> -->
        <template v-else>
          <div slot="tip" class="el-upload__tip">
            支持扩展名xlsx,xls(文件大小不超过500M),可点击
            <a @click="downloadFile">下载模版</a>
          </div>
        </template>
      </el-upload>
    </div>
    <div class="text-right mt20">
      <el-button size="small" @click="cancle">取消</el-button>
      <el-button size="small" type="primary" @click="sure">确定</el-button>
    </div>
  </div>
</template>

<script>
import { DATA_URL_FILE_UPLOAD } from '@/constant/data-url-constants';
import { Message } from 'element-ui';
import {
  getServiceTemplate,
  getAssetTemplate,
  getLabelTemplate,
  postAssetRemarkTemplate
} from '@/service/upload-update-service';
import store from '@/store';
export default {
  props: ['props'],
  data() {
    return {
      DATA_URL_FILE_UPLOAD,
      fileList: [],
      dialogType: '',
      isMore: false
    };
  },

  components: {},

  mounted() {
    this.dialogType = this.props?.dialogType || 'assetClass';
  },

  methods: {
    cancle() {
      this.$emit('cancel');
    },
    sure() {
      Message({
        message: '开始导入',
        type: 'success',
        duration: 0,
        showClose: true,
        customClass: 'export-message'
      });

      this.$refs['uploadFile'].submit();
    },
    handleChange(file, fileList) {
      if (file.size > 500 * 1024 * 1024) {
        this.$message.error('上传的文件大小请不要超过500M');
        this.fileList = fileList;
      }
    },
    handleSuccess(res, file, fileList) {
      const paramsData = {
        ...res.data,
        executorType: this.props.executorType,
        exportName: this.props.exportName,
        importType: this.props.importType
      };
      store.commit('setFileInfo', paramsData);
      this.$emit('save');
    },
    convertTimestampToISO(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);

      // 年月日部分
      const year = date.getUTCFullYear();
      const month = String(date.getUTCMonth() + 1).padStart(2, '0');
      const day = String(date.getUTCDate()).padStart(2, '0');

      // 时分秒毫秒部分
      const hours = String(date.getUTCHours()).padStart(2, '0');
      const minutes = String(date.getUTCMinutes()).padStart(2, '0');
      const seconds = String(date.getUTCSeconds()).padStart(2, '0');
      const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');

      // 时区部分
      const offset = date.getTimezoneOffset(); // 返回当前时区与UTC的分钟差（如东八区为-480）
      const offsetHours = Math.abs(Math.floor(offset / 60))
        .toString()
        .padStart(2, '0');
      const offsetMinutes = Math.abs(offset % 60)
        .toString()
        .padStart(2, '0');
      const offsetSign = offset > 0 ? '-' : '+'; // 东八区偏移为负，但符号需反转
      return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}${offsetSign}${offsetHours}:${offsetMinutes}`;
    },
    downloadErrorFile() {},
    downloadFile() {
      const apiUrlObj = {
        asset: getAssetTemplate,
        assetDesc: postAssetRemarkTemplate,
        assetClass: postAssetRemarkTemplate,
        serve: getServiceTemplate,
        lable: getLabelTemplate
      };
      let params = {};
      if (!apiUrlObj[this.dialogType]) return;
      if (this.dialogType == 'assetClass') {
        //接受过来的参数
        const { data } = this.props;
        // console.log(this.props,'this.props11111',data)
        const requestData = data || {};
        const searchConditionList = [];
        params = {
          name: '字段导出',
          description: '',
          executionType: 'IMMEDIATE',
          parameter: {
            columnList: '',
            searchConditionList: '',
            fields: []
          },
          executorType: 'columnExport'
        };
        const searchLikeFields = [
          'name',
          'comment',
          'remark',
          'samples',
          'dc_table#name',
          'dc_data_source#url',
          'dc_data_source#name',
          'dc_table#comment',
          'dc_schema#name',
          'dc_database#name'
        ];
        searchLikeFields.forEach((field) => {
          if (requestData[field]) {
            searchConditionList.push({
              fieldName: field,
              columnExp: 'like',
              value: `%${requestData[field]}%`
            });
          }
        });
        const searchEqFields = [
          'scan_type',
          'connect_type',
          'manual',
          'level_id',
          'dc_data_source#source_type_id',
          'dc_data_source#business_system'
        ];
        searchEqFields.forEach((field) => {
          if (requestData[field]) {
            searchConditionList.push({
              fieldName: field,
              columnExp: '=',
              value: requestData[field]
            });
          }
        });

        const timeFields = ['created_at', 'updated_at'];
        timeFields.forEach((field) => {
          if (requestData[field]) {
            const time = requestData[field].split(',');
            if (time.length === 2) {
              searchConditionList.push({
                fieldName: field,
                columnExp: 'between',
                startValue: this.convertTimestampToISO(parseInt(time[0])),
                endValue: this.convertTimestampToISO(parseInt(time[1]))
              });
            }
          }
        });
        const inFields = ['labels', 'tasks'];
        inFields.forEach((field) => {
          if (field == 'labels' && requestData[field] == 'no') {
            searchConditionList.push({
              fieldName: field,
              columnExp: '=',
              value: []
            });
          } else if (field == 'labels' && requestData[field] == 'has') {
            searchConditionList.push({
              fieldName: field,
              columnExp: '!=',
              value: []
            });
          } else if (requestData[field]) {
            searchConditionList.push({
              fieldName: field,
              columnExp: 'contains',
              value: [parseInt(requestData[field])]
            });
          }
        });

        const insFields = ['source_id'];
        insFields.forEach((field) => {
          if (requestData[field]) {
            const array = requestData[field].split(',').map(Number);
            searchConditionList.push({
              fieldName: field,
              columnExp: 'in',
              value: array
            });
          }
        });

        if (data?.ids != '') {
          searchConditionList.push({
            fieldName: 'id',
            columnExp: 'in',
            value: data.ids.split(',')
          });
        }
        // 处理返回字段
        const columns = [
          'comment',
          'created_at',
          'name',
          'remark',
          'samples',
          'id',
          'updated_at',
          'dc_table.name',
          'dc_data_source.url',
          'dc_data_source.host',
          'dc_data_source.port',
          'dc_data_source.name',
          'dc_data_source.business_system',
          'dc_table.comment',
          'dc_schema.name',
          'dc_level.name',
          'label_names.names',
          'manual',
          'dc_data_source.dc_data_source_type.name',
          'task_names.names',
          'dc_database.name'
        ];
        params.parameter.columnList = columns;
        params.parameter.searchConditionList = searchConditionList;
      }
      if (this.dialogType == 'assetDesc') {
        const { data } = this.props;
        const requestData = data;
        const searchConditionList = [];
        params = {
          name: '字段导出',
          description: '',
          executionType: 'IMMEDIATE',
          parameter: {
            columnList: '',
            searchConditionList: '',
            fields: []
          },
          executorType: 'columnExport'
        };
        const searchLikeFields = [
          'name',
          'comment',
          'remark',
          'samples',
          'dc_table#name',
          'dc_data_source#url',
          'dc_data_source#name',
          'dc_table#comment',
          'dc_schema#name',
          'dc_database#name'
        ];
        searchLikeFields.forEach((field) => {
          if (requestData[field]) {
            searchConditionList.push({
              fieldName: field,
              columnExp: 'like',
              value: `%${requestData[field]}%`
            });
          }
        });
        const searchEqFields = [
          'scan_type',
          'connect_type',
          'manual',
          'level_id',
          'dc_data_source#source_type_id',
          'dc_data_source#business_system'
        ];
        searchEqFields.forEach((field) => {
          if (requestData[field]) {
            searchConditionList.push({
              fieldName: field,
              columnExp: '=',
              value: requestData[field]
            });
          }
        });

        const timeFields = ['created_at', 'updated_at'];
        timeFields.forEach((field) => {
          if (requestData[field]) {
            const time = requestData[field].split(',');
            if (time.length === 2) {
              searchConditionList.push({
                fieldName: field,
                columnExp: 'between',
                startValue: this.convertTimestampToISO(parseInt(time[0])),
                endValue: this.convertTimestampToISO(parseInt(time[1]))
              });
            }
          }
        });

        const inFields = ['labels', 'tasks'];
        inFields.forEach((field) => {
          if (field == 'labels' && requestData[field] == 'no') {
            searchConditionList.push({
              fieldName: field,
              columnExp: '=',
              value: []
            });
          } else if (field == 'labels' && requestData[field] == 'has') {
            searchConditionList.push({
              fieldName: field,
              columnExp: '!=',
              value: []
            });
          } else if (requestData[field]) {
            searchConditionList.push({
              fieldName: field,
              columnExp: 'contains',
              value: [parseInt(requestData[field])]
            });
          }
        });

        const insFields = ['source_id'];
        insFields.forEach((field) => {
          if (requestData[field]) {
            const array = requestData[field].split(',').map(Number);
            searchConditionList.push({
              fieldName: field,
              columnExp: 'in',
              value: array
            });
          }
        });

        if (data?.ids != '') {
          searchConditionList.push({
            fieldName: 'id',
            columnExp: 'in',
            value: data?.ids.split(',')
          });
        }
        // 处理返回字段
        const columns = [
          'comment',
          'created_at',
          'name',
          'remark',
          'samples',
          'id',
          'updated_at',
          'dc_table.name',
          'dc_data_source.url',
          'dc_data_source.host',
          'dc_data_source.port',
          'dc_data_source.name',
          'dc_data_source.business_system',
          'dc_table.comment',
          'dc_schema.name',
          'dc_level.name',
          'label_names.names',
          'manual',
          'dc_data_source.dc_data_source_type.name',
          'task_names.names',
          'dc_database.name'
        ];
        params.parameter.columnList = columns;
        params.parameter.searchConditionList = searchConditionList;
      }

      apiUrlObj[this.dialogType](params)
        .then(async (response) => {
          const contentType =
            response.headers['content-type'] || 'application/octet-stream';
          const blob = new Blob([response.data], { type: contentType });
          if (response?.headers['content-type']?.includes('application/json')) {
            // Blob 是 JSON，转换为文本再解析
            const text = await blob.text();
            try {
              const jsonStr = JSON.parse(text);
              if (!jsonStr.success || jsonStr.status === -1) {
                this.isMore = true;
                this.$message.error(jsonStr.msg || '下载失败');
              }
            } catch (err) {
              this.isMore = true;
              this.$message.error('下载失败');
            }
          } else {
            const disposition = response.headers['content-disposition'];
            let filename = 'downloaded-file';
            if (disposition) {
              const match = disposition.match(
                /filename\*?=(?:UTF-8'')?([^;]+)/i
              );
              if (match && match[1]) {
                filename = decodeURIComponent(match[1].replace(/['"]/g, ''));
              }
            }
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(downloadUrl);
          }
        })
        .catch((error) => {
          this.$message.error(error.msg || '操作失败');
        });
    }
  }
};
</script>
<style lang="less" scoped>
.export-com {
  .upload-title {
    width: 70px;
    flex-shrink: 0;
  }
  a {
    font-size: 12px;
    cursor: pointer;
  }
  .icon-success {
    margin-right: 5px;
    color: #67c23a;
  }
  .icon-shibai {
    margin-right: 5px;
    color: #f56c6c;
  }
  .tip-context {
    color: red;
  }
}
</style>
