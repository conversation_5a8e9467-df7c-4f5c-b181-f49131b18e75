<template>
  <div class="pie-chart">
    <qz-donut-chart :chartInfo="pieData" />
  </div>
</template>

<script>
export default {
  props: ['props'],
  data() {
    return {
      pieData: {}
    };
  },

  components: {},

  mounted() {
    const { echartSql, echartData, title } = this.props;
    if (!echartData) {
      const chartData = [];
      let eventCount = 0;
      const isChartData = true;
      const dataList = [
        {
          name: '互联网',
          count: 11078
        },
        {
          name: '境外互联网',
          count: 5604
        },
        {
          name: '境内互联网',
          count: 1049
        },
        {
          name: '互联网-境内',
          count: 523
        },
        {
          name: '局域网-生产网',
          count: 1049
        },
        {
          name: '局域网-测试网',
          count: 523
        }
      ];
      dataList.forEach((item) => {
        eventCount += item.count;
        chartData.push({
          value: item.count,
          name: item.name
        });
      });
      this.pieData = {
        chartName: title ? title : '',
        chartData: chartData,
        chartCount: eventCount,
        isChartData: isChartData,
        size: 'large'
      };
    }
  },

  methods: {}
};
</script>
<style lang="less" scoped></style>
