<template>
  <div class="dataManage">
    <api-table
      ref="table"
      table-id="data-list"
      :data-source="getDataList"
      title="数据管理"
      tools-layout="divider,add"
    >
      <api-table-tool-register id="add">
        <el-button type="primary" size="mini" @click="openDataDrawalert({})"
          >新增数据组</el-button
        >
      </api-table-tool-register>
      <api-table-column type="selection"> </api-table-column>
      <api-table-column label="分组名称" prop="groupName"
        ><template #default="{ row }">
          <span @click="openDataDrawalert(row)">{{ row.groupName||'--' }}</span>
        </template></api-table-column
      >
      <api-table-column label="分组描述" prop="groupDesc" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.groupDesc || '--' }}
        </template>
      </api-table-column>
      <api-table-column label="账号" prop="userIds"></api-table-column>
      <api-table-column label="操作" width="160" fixed="right">
        <template #default="{ row }">
          <span class="action-link" @click="deleteItem(row)">删除</span>
          <span class="action-link" @click="assignAccount(row)">
            分配账号
          </span>
          <span class="action-link" @click="openDataDrawalert(row)">
            编辑
          </span>
        </template>
      </api-table-column>
    </api-table>
  </div>
</template>
<script>
import {
  postDataManageList,
  postDataManageAdd,
  postDataManageEdit,
  postDataManageDelete,
  postDataManageAssignAccount,
  postDataManageBatchDelete,
  postDataManageBatchAssignAccount
} from '@/service/data-manage-service';
export default {
  data() {
    return {
      // dataManageList: []
    };
  },

  components: {},

  mounted() {},

  methods: {
    getDataList(params) {
      return postDataManageList(params);
    },
    openDataDrawalert(row) {
      this.$DrawAlert({
        title: row.id ? '编辑分组' : '新增分组',
        params: {
          detail: row || {},
          callback: () => {
            if (row.id) {
              this.$refs.table.reloadCurrentPage();
            } else {
              this.$refs.table.reload();
            }
          }
        },
        componentObj: {
          // component:()=>{import('./packages/data-group.vue')}
        }
      });
    },
    assignAccount(row) {
      this.$dialogAlert({
        params: {
          detail: row,
          callBack: () => {
            this.$refs.table.reloadCurrentPage();
          }
        },
        component: () => import('./packages/assign-account-detail.vue'),
        alertTitle: '分配账号',
        alertWidth: '500px',
        alertHeight: 'auto'
      });
    },
    deleteItem(row) {
      this.$confirm('确认删除？').then(() => {
        this.$refs.table.loading = true;
      });
    }
  }
};
</script>
<style lang="less" scoped></style>
