<!--
 * @Fileoverview: 系统升级
 * @Description: 配置-系统升级
-->
<template>
  <div class="system-update">
    <div class="system-update__content">
      <el-form class="qz-form" label-width="70px">
        <el-form-item label="系统名称">{{ productName }}</el-form-item>
        <el-form-item label="系统版本">{{ version }}</el-form-item>
      </el-form>
      <div class="system-update__content__btn">
        <el-button
          class="mr20"
          size="small"
          type="primary"
          @click="uploadUpdate"
        >
          上传并更新
        </el-button>
        <el-button size="small" type="primary" @click="drawerVisible = true">
          查看升级日志
        </el-button>
      </div>
    </div>
    <qz-api-drawer
      :visible.sync="drawerVisible"
      destroy-on-close
      title="系统升级日志"
      size="70%"
    >
      <UpdateLog />
    </qz-api-drawer>
    <sance />
    <!-- <classLevel/> -->
  </div>
</template>
<script>
import UpdateLog from './packages/system-update/update-log.vue';
import { getPageMenuAndBlockConfigs } from '@/utils/storage-utils';
import sance from '@/components-react/qz-components/asset-sance/index.vue';
import classLevel from '@/components-react/qz-components/class-level/index.vue';
import { mapState } from 'vuex';
export default {
  components: { UpdateLog, sance, classLevel },
  data() {
    return {
      // productName:"数据分类分级系统",
      version: '版本号',
      drawerVisible: false
    };
  },
  methods: {
    uploadUpdate() {
      this.$dialogAlert({
        params: {
          uploadFlag: true,
          callBack: () => {}
        },
        isClickNotClose: true,
        component: () => import('./packages/system-update/upload-update.vue'),
        alertWidth: '700px',
        alertHeight: 'auto',
        alertTitle: '上传并更新'
      });
    }
  },
  computed: {
    ...mapState(['productName'])
  },
  mounted() {
    const data = getPageMenuAndBlockConfigs();
    // this.productName = data.productName;
    this.version = data.version;
  }
};
</script>
<style lang="less" scoped>
.system-update {
  &__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 50px auto;
    &__btn {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .el-form-item__content {
      color: @text-tip-color;
    }
  }
}
</style>
