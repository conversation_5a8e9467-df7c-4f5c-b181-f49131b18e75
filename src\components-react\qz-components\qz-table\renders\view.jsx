import {
  getScrollParent,
  getStyleNumber,
  offset,
  position,
  registerRenderer
} from 'amis-core';
import React from 'react';
import '../styles/view.less';
import { Input, Spinner } from 'amis-ui';
import { TreeSelector } from 'amis-ui/lib/components/Tree';
import { filterTree } from '../../utils/helper';

class CrudView extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      options: [],
      filteredOptions: [],
      selectedOptionValue: null, // 选中的值
      virtualHeight: 300, // 默认虚拟高度
      filterText: ''
    };

    this.treeSelector = React.createRef();
    this.viewRef = React.createRef();

    this.handleFilterChange = this.handleFilterChange.bind(this);
    this.itemRender = this.itemRender.bind(this);
  }

  componentDidMount() {
    this.loadOptions();
    this.autoTreeHeight();
    window.addEventListener('resize', this.autoTreeHeight.bind(this));
  }

  componentDidUpdate(prevProps, prevState) {
    // 如果选中的值发生变化或者过滤条件发生变化，更新高亮状态
    if (
      this.state.selectedOptionValue !== prevState.selectedOptionValue ||
      this.state.filterText !== prevState.filterText
    ) {
      // 组件渲染完成以后再高亮
      setTimeout(() => {
        this.highlightSelectedItem();
      });
    }
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.autoTreeHeight.bind(this));
  }

  autoTreeHeight() {
    const viewContent = this.viewRef.current;

    if (!viewContent) {
      return;
    }

    viewContent.removeAttribute('style');

    // 计算 view-content 在 dom 中的位置
    let viewportHeight = window.innerHeight;
    let viewContentTop = offset(viewContent).top;

    const parent = getScrollParent(viewContent.parentElement);
    if (parent && parent !== document.body) {
      viewportHeight = parent.clientHeight - 1;
      viewContentTop = position(viewContent, parent).top;
    }

    let viewContentBottom = 0;
    let selfNode = viewContent;
    let parentNode = selfNode.parentElement;
    while (parentNode) {
      const paddingBottom = getStyleNumber(parentNode, 'padding-bottom');
      const borderBottom = getStyleNumber(parentNode, 'border-bottom-width');
      const marginBottom = getStyleNumber(selfNode, 'margin-bottom');
      viewContentBottom += paddingBottom + borderBottom + marginBottom;

      selfNode = parentNode;
      parentNode = selfNode.parentElement;
      if (parent && parent !== document.body && parent === selfNode) {
        break;
      }
    }

    const viewContentHeight = Math.round(
      viewportHeight - viewContentTop - viewContentBottom
    );

    if (viewContentHeight > 0) {
      viewContent.style.height = `${viewContentHeight}px`;
      viewContent.style.overflow = 'auto';
      this.setState({
        virtualHeight: viewContentHeight - 65 // 减去搜索框的高度
      });
    }
  }

  async loadOptions() {
    const { source, env } = this.props;
    if (source) {
      this.setState({ loading: true });
      const response = await env.fetcher(source);
      const options = response?.data?.rows || [];
      this.setState({ options, filteredOptions: options, loading: false });
    }
  }

  handleFilterChange(e) {
    const { labelField = 'label' } = this.props;
    const keywords = e.currentTarget.value;
    this.setState({ filterText: keywords });
    const { options } = this.state;
    if (keywords) {
      const filteredOptions = filterTree(options, (item) => {
        return item[labelField].toLowerCase().includes(keywords.toLowerCase());
      });
      this.setState({ filteredOptions });
    } else {
      this.setState({ filteredOptions: options });
    }
  }

  handleTreeChange(value) {
    const { dispatchEvent } = this.props;
    this.setState({
      selectedOptionValue: value
    });
    dispatchEvent('change', {
      selectedItems: [value]
    });
  }

  highlightSelectedItem() {
    const treeComp = this.treeSelector.current;
    const treeEl = treeComp?.root?.current;
    if (treeEl) {
      // 先删除所有父元素的 is-checked 类
      const parentItems = treeEl.querySelectorAll('.cxd-Tree-item.is-checked');
      parentItems.forEach((item) => {
        item.classList.remove('is-checked');
      });

      // 将 cxd-Tree-itemLabel is-checked 的父元素 cxd-Tree-item 也添加 is-checked 类
      const checkedItems = treeEl.querySelectorAll(
        '.cxd-Tree-itemLabel.is-checked'
      );
      checkedItems.forEach((item) => {
        const parentItem = item.closest('.cxd-Tree-item');
        if (parentItem) {
          parentItem.classList.add('is-checked');
        }
      });
    }
  }

  itemRender(data) {
    const { labelField = 'label', countField = 'count' } = this.props;
    const textContent =
      data.children && data.children.length > 0
        ? data[labelField]
        : `${data[labelField]} (${data[countField] || 0})`;
    // TODO 自定义渲染树形选择器的节点
    return (
      <div>
        <span>{textContent}</span>
      </div>
    );
  }

  render() {
    const { ...rest } = this.props;
    return (
      <div ref={this.viewRef} className="crud-view">
        <div className="crud-view__search-box">
          <Input
            placeholder="搜索视图"
            value={this.state.filterText}
            className="crud-view__search-inner"
            onChange={this.handleFilterChange}
          ></Input>
          <i className="crud-view__search-suffix el-icon-search"></i>
        </div>
        {this.state.loading && <Spinner size="sm" />}
        <TreeSelector
          {...rest}
          ref={this.treeSelector}
          className="crud-view__tree-selector"
          initiallyOpen={true}
          onlyLeaf={true}
          showIcon={false}
          nodeBehavior={['check', 'unfold']}
          options={this.state.filteredOptions}
          itemRender={this.itemRender}
          itemHeight={40}
          joinValues={false}
          virtualHeight={this.state.virtualHeight || 300}
          value={this.state.selectedOptionValue}
          onChange={(value) => this.handleTreeChange(value)}
        ></TreeSelector>
      </div>
    );
  }
}

registerRenderer({
  type: 'crud-view',
  component: CrudView
});
