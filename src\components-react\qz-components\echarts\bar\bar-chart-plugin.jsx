import { BasePlugin, getSchemaTpl } from 'amis-editor';
const rendererName = 'qz-bar-chart-render';
export class BarChartPlugin extends BasePlugin {
  // 这里要跟对应的渲染器名字对应上
  // 注册渲染器的时候会要求指定渲染器名字
  rendererName = rendererName;
  // 暂时只支持这个，配置后会开启代码编辑器
  $schema = '/schemas/UnknownSchema.json';
  //组件关键字，用来辅助组件列表搜索
  searchKeywords = '';
  // 用来配置名称和描述
  name = '柱状图组件';
  title = '';
  description = '水平柱状图/垂直柱状图组件';

  // tag，决定会在哪个 tab 下面显示的
  tags = ['全知组件'];

  // 图标
  icon = 'fa fa-user';

  // 用来生成预览图的
  previewSchema = {
    type: rendererName
  };

  // 拖入组件里面时的初始数据
  scaffold = {
    type: rendererName,
    chartType: 'horizontal',
    echartSql: '',
    echartData: ''
  };

  // 右侧面板相关
  panelBodyCreator = (context) => {
    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: [
          getSchemaTpl('layout:originPosition', { value: 'left-top' }),
          getSchemaTpl('description', {
            name: 'echartSql',
            label: 'echartSql内容',
            maxRows: 10,
            rows: 10,
            placeholder: '请输入echartSql内容'
          }),
          {
            type: 'select',
            name: 'chartType',
            label: '柱状图类型',
            options: [
              { label: '水平柱状图', value: 'horizontal' },
              { label: '垂直柱状图', value: 'vertical' }
            ],
            placeholder: '请选择柱状图类型'
          },
          getSchemaTpl('description', {
            name: 'echartData',
            label: '数据值',
            maxRows: 10,
            rows: 10,
            placeholder: '可以为空,用于获取后端接口返回的echart数据'
          })
        ]
      }
    ]);
  };
}
