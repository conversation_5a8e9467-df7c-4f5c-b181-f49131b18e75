import { render as renderAmis } from 'amis';
import 'amis/lib/themes/cxd.css';
import 'amis-ui/lib/themes/cxd.css';
import '@/assets/css/override-amis.less';
import 'amis/lib/helper.css';
import './amis-iconfont/iconfont.css';
import { axios } from '@quanzhiFE/qz-frontend';
/**
 * amis底层是通过import()的方式引入monaco-editor，在本项目中会报错
 * 所以这里需要手动引入monaco-editor，避免报错
 */
// eslint-disable-next-line no-unused-vars
import './qz-components/json-scheam/render';
import './qz-components/asset-sance/render';
import './qz-components/class-level/render';
import './qz-components/data-lable/render';
import './qz-components/export-com/render';
import './qz-components/amis-tree/render';
import './qz-components/qz-table/renders';
import './qz-components/samples/render';
import './qz-components/echarts/bar/bar-chart-render';
import './qz-components/echarts/pie/pie-chart-render';
import { Message } from 'element-ui';
export default function preview({ schema, params }) {
  return renderAmis(
    schema,
    {
      // 这里可以传入自定义的 props
      data: params || {}
    },
    {
      fetcher: axios,
      notify: (type, msg) => {
        if (msg) {
          Message[type](msg);
        }
      },
      alert: (msg) => {
        alert(msg);
      }
    }
  );
}
