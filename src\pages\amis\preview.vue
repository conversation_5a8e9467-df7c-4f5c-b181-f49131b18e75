<template>
  <div
    class="react-container"
    v-loading="!showDom"
  >
    <!-- <el-button @click="testPdf">导出</el-button> -->
    <react-proxy
      v-if="showDom"
      ref="reportPreview"
      :component="ReportPreview"
      :props="{ schema, params: sparams }"
    ></react-proxy>
  </div>
</template>
<script>
import ReportPreview from '@/components-react/report-preview.jsx';
import { getAmisConfig } from '@/service/role-service';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
export default {
  props: ['params'],
  data () {
    return {
      ReportPreview,
      schema: {},
      sparams: {}, //额外的参数
      showDom: false,
      loading: false
    };
  },
  components: {

  },
  watch: {
    $route: {
      handler (val) {
        this.showDom = false;
        if (this.$route.query?.acode) {
          getAmisConfig({ resourceCode: this.$route.query?.acode }).then(
            (res) => {
              this.schema = JSON.parse(res?.data[0]?.pageConfig);
              this.showDom = true;
            }
          );
        } else {
          this.schema = localStorage.getItem('amisdata')
            ? JSON.parse(localStorage.getItem('amisdata'))
            : {};
          this.showDom = true;
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted () { },
  methods: {
    testPdf () {
      const ele = document.querySelector('.cxd-Page-main'); // 页面中多个区域
      html2canvas(ele, {
        useCORS: true,
        scale: 1,
        ignoreElements: e => {
          if (
            e.contains(ele) ||
            ele.contains(e) ||
            e.tagName === 'STYLE' ||
            e.tagName === 'LINK' ||
            e.getAttribute('data-html2canvas') != null // header里面的样式不能筛掉
          ) {
            // console.log(e);
            return false;
          }
          return true;
        }
      })
        .then(canvas => {
          const dataUrl = canvas.toDataURL('image/jpeg');
          const img = new Image();
          img.src = dataUrl;
          img.onload = () => {
            const imgWidth = 297; // A4 宽度 mm
            const pageHeight = 400; // A4 高度 mm
            const imgHeight = (img.height / img.width) * imgWidth;
            let heightLeft = imgHeight;
            let position = 0;

            const pdf = new jsPDF('p', 'mm', 'a3');
            pdf.addImage(img, 'JPEG', 4, position, imgWidth - 6, pageHeight);
            heightLeft -= pageHeight;

            // while (heightLeft > 0) {
            //   position = heightLeft - imgHeight;
            //   pdf.addPage();
            //   pdf.addImage(img, 'JPEG', 3, position, imgWidth + 10, imgHeight);
            //   heightLeft -= pageHeight;
            // }

            pdf.save('页面导出.pdf');
          };
        })
        .catch(function (error) {
          console.log('22222', error);
        })
    }





  }
};
</script>

<style lang="less" scoped>
.react-container {
  height: 100%;
}
::v-deep .ae-Editor {
  height: 100%;
  .ae-RendererList-item .icon-box::before {
    display: none;
  }
}
</style>
