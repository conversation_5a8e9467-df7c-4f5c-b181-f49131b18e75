<template>
  <div class="react-container" v-loading="!showDom">
    <react-proxy
      v-if="showDom"
      :component="ReportPreview"
      :props="{ schema, params: sparams }"
    ></react-proxy>
  </div>
</template>
<script>
import ReportPreview from '@/components-react/report-preview.jsx';
import { getAmisConfig } from '@/service/role-service';
export default {
  props: ['params'],
  data() {
    return {
      ReportPreview,
      schema: {},
      sparams: {}, //额外的参数
      showDom: false
    };
  },
  watch: {
    $route: {
      handler(val) {
        this.showDom = false;
        if (this.$route.query?.acode) {
          getAmisConfig({ resourceCode: this.$route.query?.acode }).then(
            (res) => {
              this.schema = JSON.parse(res?.data[0]?.pageConfig);
              this.showDom = true;
            }
          );
        } else {
          this.schema = localStorage.getItem('amisdata')
            ? JSON.parse(localStorage.getItem('amisdata'))
            : {};
          this.showDom = true;
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {}
};
</script>

<style lang="less" scoped>
.react-container {
  height: 100%;
}
::v-deep .ae-Editor {
  height: 100%;
  .ae-RendererList-item .icon-box::before {
    display: none;
  }
}
</style>
