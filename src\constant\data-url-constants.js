/**
 * 接口前缀
 */
// export const APP_BASE = '/app-operation';
export const APP_BASE = '/dc';

export const DATA_URL_UPLOAD_UPDATE_ADDRESS = `${APP_BASE}/api/upload/file`;

export const DATA_URL_UPLOAD_UPDATE = `${APP_BASE}/api/sysUpgrade/start`;

//升级日志
export const DATA_URL_UPDATE_LOG = `${APP_BASE}/cpt/graphql/v1/pageList/cpt_upgrade_log`;

export const DATA_URL_BASIC_CONFIG_SAVE = '/upload/img';

export const DATA_URL_PREVIEW_AUTHORIZATION = '/autss';

//登录验证码
export const DATA_URL_CAPTCHA = `${APP_BASE}/cpt/login/captcha`;
// export const DATA_URL_CAPTCHA=`${APP_BASE}/api/login/captcha`
//登录
export const DATA_URL_LOGIN = `${APP_BASE}/cpt/login/doLogin`;
//退出登录
export const DATA_URL_LOGIN_OUT = `${APP_BASE}/cpt/login/logout`;
//账号相关
export const DATA_URL_ACCOUNT = `${APP_BASE}/cpt/user`;
//角色相关
export const DATA_URL_ROLE_LIST = `${APP_BASE}/cpt/role/list`;
//审计日志相关
export const DATA_URL_AUDIT_LOG = `${APP_BASE}/cpt/audit-log`;

export const DATA_URL_SYSLOG_EXPORT = `${APP_BASE}/cpt/audit-log/export`;

export const DATA_URL_SYSLOG_MODULE = `${APP_BASE}/cpt/audit-log/module`;
//开放接口相关
export const DATA_URL_OPEN_API = `${APP_BASE}/cpt/openapi`;

//获取配置相关
export const DATA_URL_DETAIL_SYSCONFIG = `${APP_BASE}/cpt/sysConfig/getString/loginConfig`;
export const DATA_URL_DETAIL = `${APP_BASE}/cpt/sysConfig/getString`;
export const DATA_URL_CONFIG = `${APP_BASE}/cpt/sysConfig`;
export const DATA_URL_DETAIL_SYSCONFIGS = `${APP_BASE}/cpt/sysConfig/getConfigs`;
export const DATA_URL_SAVE_SYSCONFIG = `${APP_BASE}/cpt/sysConfig/saveConfigs`;

//系统授权相关
export const DATA_URL_SYS_AUTH = `${APP_BASE}/api/license`;

//数据推送相关
export const DATA_URL_DATA_SEND = `${APP_BASE}/api/pushTask`;
export const DATA_URL_DATA_SEND_CONFIG = `${APP_BASE}/cpt/data-push/pushConfig`;
export const DATA_URL_DATA_PUSH_TASK_ADD = `${APP_BASE}/api/pushTask/add`;
export const DATA_URL_DATA_PUSH_TASK_DELETE = `${APP_BASE}/api/pushTask/delete`;
export const DATA_URL_DATA_PUSH_TASK_UPDATE = `${APP_BASE}/api/pushTask/update`;
export const DATA_URL_DATA_PUSH_TASK_DETAIL = `${APP_BASE}/api/pushTask/detail`;
export const DATA_URL_SUBSCRIBE_CUSTOM_FIELDS = `${APP_BASE}/api/metadata/columnFields`;
export const DATA_URL_SUBSCRIBE_LOG_LIST = `${APP_BASE}/cpt/data-push/pushLog/list`;

//策略模版配置
export const DATA_URL_STRATEGY_TEMPLATE = `${APP_BASE}/cpt/lowcode`;

//获取菜单接口
export const DATA_URL_MENU = `${APP_BASE}/cpt/resource/tree`;

//根据编码获取amis配置
export const DATA_URL_AMIS_CONFIG = `${APP_BASE}/cpt/lowcode/findByResourceCode`;

//获取数据库服务类型
export const DATA_URL_DC_DAT_TYPE = `${APP_BASE}/cpt/graphql/v1/distinct/dc_data_source_type/name`;

export const DATA_URL_DC_DRIVER_LIST = `${APP_BASE}/cpt/graphql/v1/pageList/dc_driver`;

export const DATA_URL_DC_UI_CONFIG = `${APP_BASE}/cpt/graphql/v1/detail/dc_data_source_type`;

export const DATA_URL_DC_DAT_TEST = `${APP_BASE}/api/dataSource/testing`;
export const DATA_URL_DC_DAT_SAVE = `${APP_BASE}/api/dataSource/save`;
//资产扫描
export const DATA_URL_SOURCE_LIST = `${APP_BASE}/cpt/graphql/v1/pageList/dc_data_source`;
export const DATA_URL_SOURCE_TREE = `${APP_BASE}/cpt/graphql/v1/treeNodes`;
export const DATA_URL_TEST = `${APP_BASE}/api/dataSource/testing`;
export const DATA_URL_SANCE_ADD = `${APP_BASE}/api/scanTask/add`;
export const DATA_URL_DATABASE_LIST = `${APP_BASE}/cpt/graphql/v1/pageList/dc_database`;
export const DATA_URL_SORCE_DETAIL = `${APP_BASE}/cpt/graphql/v1/detail/dc_data_source`;
export const DATA_URL_TREE = `${APP_BASE}/cpt/graphql/v1/treeNodes`;
export const DATA_URL_SANCE_DETAIL = `${APP_BASE}/cpt/graphql/v1/detail/dc_scan_task`;
// JDBCURL渲染相关
export const DATA_URL_JDBCURL_RENDER = `${APP_BASE}/api/dataSource/renderJdbcUrl`;
export const DATA_URL_JDBCURL_PARSE = `${APP_BASE}/api/dataSource/parseJdbcUrl`;

//分类分级
export const DATA_URL_CLASS_ADD = `${APP_BASE}/api/classification/add`;
export const DATA_URL_CLASS_DETAIL = `${APP_BASE}/cpt/graphql/v1/detail/dc_classification_job`;
export const DATA_URL_TASK_LIST = `${APP_BASE}/cpt/graphql/v1/pageList/dc_scan_task`;
export const DATA_URL_AI_LIST = `${APP_BASE}/cpt/graphql/v1/pageList/dc_ai_model`;
export const DATA_URL_LABEL_LEVEL = `${APP_BASE}/cpt/graphql/v1/distinct/dc_level/name`;
export const DATA_URL_LAEL_CLASS = `${APP_BASE}/cpt/graphql/v1/distinct/dc_label/name`;
export const DATA_UTL_CLASS_ADD = `${APP_BASE}/api/label/save`;
export const DATA_URL_RULE = `${APP_BASE}/api/label/feature`;
export const DATA_URL_LABEL_DETAIL = `${APP_BASE}/cpt/graphql/v1/detail/dc_label`;
export const DATA_URL_FILED_LIST = `${APP_BASE}/cpt/graphql/v1/pageList/dc_column`;
export const DATA_URL_FILED_TEST = `${APP_BASE}/api/label/testing`;

// 获取方言列表
export const DATA_URL_DIALECT_LIST = `${APP_BASE}/cpt/graphql/v1/distinct/dc_driver/dialect`;
// 上传文件通用接口
export const DATA_URL_FILE_UPLOAD = `${APP_BASE}/api/upload/file`;
//获取上传文件任务id-通用接口
export const DATA_URL_FILE_TASK_ADD = `${APP_BASE}/api/commonTask/add`;
//获取上传文件任务状态  --通用接口
export const DATA_URL_FILE_STATUS = `${APP_BASE}/cpt/graphql/v1/detail/dc_common_job`;
// 下载服务模板
export const DATA_URL_FILE_SERVICE_TEMPLATE = `${APP_BASE}/api/metadata/downloadTemplate/dataSource`;
//下载资产模版
export const DATA_URL_ASSET_TEMPLATE = `${APP_BASE}/api/metadata/downloadTemplate/column`;
//导入标签模版
export const DATA_URL_LABEL_IMPORT = `${APP_BASE}/api/label/downloadTemplate`;
//下载资产备注&分类模版
export const DATA_URL_ASSET_REMARK_TEMPLATE = `${APP_BASE}/api/metadata/downloadTemplate/columns`;

// 新增/更新数据库类型
export const DATA_URL_SERVICE_TYPE_SAVE = `${APP_BASE}/api/dataSourceType/save`;
// 新增驱动版本
export const DATA_URL_SERVICE_VERSION_SAVE = `${APP_BASE}/api/driver/create`;
// 数据源详情
export const DATA_URL_DATASOURCE_DETAIL = `${APP_BASE}/cpt/graphql/v1/detail/dc_data_source`;

//数据服务懒加载接口
export const DATA_URL_GET_DATABASE = `${APP_BASE}/api/dataSource/getDatabases`;

//树状懒加载接口--通用
export const DATA_URL_TREE_DATA = `${APP_BASE}/cpt/graphql/v1/treeNodes`;

//显示模版提示
export const DATA_URL_TEMPLATE = `${APP_BASE}/cpt/graphql/v1/detail/dc_template`;

// 数据血缘
export const URL_GET_DATA_KINSHIP_TABLE_DATA = `${APP_BASE}/api/dataConsanguinity/checkTableDataConsanguinity`;
// 数据管理
export const DATA_URL_DATA_MANAGE_SAVE = `${APP_BASE}/api/group/save`;
export const DATA_URL_DATA_MANAGE_LIST = `${APP_BASE}/api/group/page`;
export const DATA_URL_DATA_MANAGE_DELETE = `${APP_BASE}/api/group/delete`;
export const DATA_URL_DATA_MANAGE_ASSIGN_ACCOUNT = `${APP_BASE}/api/group/assignUsers`;
export const DATA_URL_DATA_MANAGE_BATCH_DELETE = `${APP_BASE}/api/group/batchDelete`;
export const DATA_URL_DATA_MANAGE_BATCH_ASSIGN_ACCOUNT = `${APP_BASE}/api/group/batchAssignUsers`;
// 服务
export const DATAURL_DATA_MANAGE_SERVICE_NAME = `${APP_BASE}/api/dataSourceType/list`;
