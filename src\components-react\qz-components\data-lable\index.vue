<template>
  <div class="class-level">
    <div class="tip-context mb20">
      将对指定资产的分级结果进行全量覆盖，已锁定的分级结果不会被覆盖。
    </div>
    <el-form
      ref="lableForm"
      :disabled="isDetail"
      :model="lableForm"
      :rules="rules"
      label-position="right"
      label-width="100px"
      size="small"
    >
      <el-form-item label="分类名称:" prop="name">
        <el-input
          v-model="lableForm.name"
          placeholder="请输入分类名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="分类描述:" prop="description">
        <el-input
          v-model="lableForm.description"
          placeholder="请输入分类描述"
          clearable
        />
      </el-form-item>
      <el-form-item label="分类类型:" prop="lableClassId">
        <el-select
          ref="selectRef"
          class="width-full"
          key="lableClassId"
          filterable
          v-model="lableForm.lableClassId"
          placeholder="请选择分类类型"
        >
          <el-option
            v-for="(item, key) in labelList"
            :key="item.id"
            :label="item.full_name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分类等级:" prop="lableLeveld">
        <el-select
          key="lableLeveld"
          class="width-full"
          v-model="lableForm.lableLeveld"
          placeholder="请选择分类等级"
        >
          <el-option
            v-for="(item, key) in labelLevleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="匹配规则:" prop="increment">
        <qz-pro-table :data-source="regTableList" border :pageVisible="false">
          <qz-table-column width="120" type="index" label="条件序号" />
          <qz-table-column prop="featureName" label="指标">
            <template slot-scope="{ row }">
              <div class="flex">
                <el-select
                  class="width-full"
                  key="indicator"
                  v-model="row.featureName"
                  @change="changeVal($event, row)"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    v-for="item in indicatorList"
                    :label="item.title"
                    :value="item.name"
                    :key="item.name"
                  ></el-option>
                </el-select>
                <el-select
                  class="width-full"
                  key="indicatorChild"
                  v-model="row.feature"
                  @change="changeChildVal($event, row)"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    v-for="item in row.indicatorChildList"
                    :label="item.title"
                    :value="item.name"
                    :key="item.name"
                  ></el-option>
                </el-select>
              </div>
            </template>
          </qz-table-column>
          <qz-table-column prop="operator" label="操作符">
            <template slot-scope="{ row }">
              <el-select
                class="width-full"
                key="reg"
                v-model="row.operator"
                @change="changeReg($event, row)"
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in row.optRegList"
                  :label="item.title"
                  :value="item.name"
                  :key="item.name"
                ></el-option>
              </el-select>
            </template>
          </qz-table-column>
          <qz-table-column prop="value" label="内容">
            <template slot-scope="{ row }">
              <el-input v-model="row.value" placeholder="请输入" size="small" />
              <!-- <el-select v-else class="width-full" key="algorith" v-model="row.value" placeholder="请选择" size="small">
                            <el-option v-for="item in algorithmList" :label="item.label" :value="item.value" :key="item.value"></el-option>
                        </el-select> -->
            </template>
          </qz-table-column>
          <qz-table-column v-if="!isDetail" width="80" label="操作">
            <template slot-scope="scope">
              <span class="delete-btn" @click="deleteRule(scope.$index)"
                >删除</span
              >
            </template>
          </qz-table-column>
        </qz-pro-table>
        <div v-if="!isDetail" class="add_btn" @click="addRule">增加规则</div>
      </el-form-item>
      <el-form-item label="规则关系:" prop="ruleCon">
        <div class="rule-flex">
          <el-radio-group v-model="lableForm.ruleCon">
            <el-radio :label="1">全部为或</el-radio>
            <el-radio :label="2">全部为且</el-radio>
            <el-radio :label="3">自定义</el-radio>
          </el-radio-group>
          <el-input
            class="cus_input"
            v-if="lableForm.ruleCon == 3"
            v-model="lableForm.logic"
            placeholder="与&，或|，非! ，也可使用中英文小括号()控制优先生效"
          />
        </div>
      </el-form-item>
      <el-form-item v-if="!isDetail" prop="test">
        <el-tooltip
          slot="label"
          content="打标失败，所选字段未匹配上此分类规则！ 打标成功，所选字段成功匹配上此分类规则！"
          placement="top"
        >
          <span>测试字段:<i class="el-icon-info ml5"></i></span>
        </el-tooltip>
        <div class="flex">
          <el-select
            class="width_full"
            filterable
            remote
            v-model="testFiled"
            :remote-method="getFildFList"
            :loading="loading"
            placeholder="请选择测试字段"
          >
            <el-option
              v-for="item in testFiledList"
              :label="item.name"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
          <el-button class="ml10" type="primary" @click="testFiledVal"
            >测试</el-button
          >
        </div>
      </el-form-item>
    </el-form>
    <div class="text-right" v-if="!isDetail">
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" @click="sure">确定</el-button>
    </div>
  </div>
</template>

<script>
import { deepCopy } from '@/utils/deep-copy';
import {
  postFiledClass,
  postFiledLevel,
  postFiledLabelAdd,
  postRules,
  postLabelDetail,
  postFiledList,
  postFiledTest
} from '@/service/class-level-service';
export default {
  props: ['props'],
  data() {
    return {
      lableForm: {
        name: '',
        description: '',
        ruleCon: 1,
        logic: '',
        lableLeveld: '',
        lableClassId: ''
      },
      testFiled: '',
      testFiledList: [],
      loading: false,
      labelList: [],
      labelLevleList: [],
      featureList: [],
      indicatorList: [],
      optRegList: [],
      algorithmList: [],
      indicatorChildList: [],
      regTableList: [
        {
          featureName: '',
          feature: '',
          operator: '',
          value: '',
          indicatorChildList: [],
          optRegList: []
        }
      ],
      rules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
        lableClassId: [
          { required: true, message: '请选择分类类型', trigger: 'change' }
        ],
        increment: [
          {
            required: true,
            message: '请输入匹配规则',
            validator: (rule, value, callback) => {
              let flag = true;
              for (let i = 0; i < this.regTableList.length; i++) {
                const vals = Object.values(this.regTableList[i]);
                if (vals.includes('') || vals.length == 0) {
                  flag = false;
                  break;
                }
              }
              if (!flag) {
                return callback(new Error('请输入完整的匹配规则'));
              }
              callback();
            },
            trigger: 'change'
          }
        ],
        lableLeveld: [
          { required: true, message: '请选择分类等级', trigger: 'change' }
        ]
      },
      detailId: '',
      featureOptions: {},
      isDetail: false
    };
  },

  components: {},

  async created() {
    const fParams = {
      isPageQuery: false,
      searchConditionList: [
        { fieldName: 'full_name', columnExp: '!=', value: null },
        { fieldName: 'dc_template#used', columnExp: '=', value: true },
        { fieldName: 'label', columnExp: '=', value: false }
      ],
      columnList: ['full_name', 'id']
    };
    const lParams = {
      isPageQuery: false,
      searchConditionList: [
        { fieldName: 'name', columnExp: '!=', value: null },
        { fieldName: 'del_flag', columnExp: '=', value: false }
      ],
      columnList: ['name', 'id']
    };
    //根据当前模板获取标签分类
    const filedRes = await postFiledClass(fParams);
    this.labelList = (filedRes?.data?.rows || []).map((item) => {
      return {
        full_name:
          item.full_name.length > 100
            ? item.full_name.substr(0, 100) + '...'
            : item.full_name,
        id: item.id
      };
    });

    const classRes = await postFiledLevel(lParams);
    this.labelLevleList = classRes?.data?.rows || [];
    const featureRes = await postRules();
    this.featureList = featureRes.data?.features || [];
    this.featureOptions = featureRes.data?.options || {};
    this.indicatorList = this.featureList.map((item) => {
      return {
        name: item.name,
        title: item.title
      };
    });

    if (this.props?.data?.id) {
      this.detailId = this.props?.data?.id;
    }
    //新增默认
    if (this.detailId) {
      if (this.props?.data?.draw_type != 'modify') {
        this.isDetail = true;
      }
      const p = {
        searchConditionList: [
          { fieldName: 'id', columnExp: '=', value: this.detailId }
        ],
        columnList: [
          'id',
          'name',
          'description',
          'level_id',
          'rule',
          'parent_id'
        ]
      };
      const detailRes = await postLabelDetail(p);
      const detailResData = detailRes?.data || {};
      this.lableForm.name = detailResData.name;
      this.lableForm.description = detailResData.description;
      this.lableForm.lableClassId = detailResData.parent_id;
      this.lableForm.lableLeveld = detailResData.level_id;
      if (detailResData.rule) {
        const parseRules = JSON.parse(detailResData.rule);
        let ruleConditions = [];
        let logic = '';
        if (parseRules.length > 0) {
          ruleConditions = parseRules[0]?.rule?.conditions || [];
          logic = parseRules[0]?.rule?.expr || '';
        }
        this.regTableList = (ruleConditions || []).map((item) => {
          const featureName = this.getFeatureName(item.key);
          const indicatorChildList = this.changeVal(featureName);
          const optRegList = this.changeChildVal(
            item.key,
            '',
            indicatorChildList
          );
          return {
            featureName: featureName,
            feature: item.key,
            value: item.content,
            operator: item.operateType,
            indicatorChildList: indicatorChildList,
            optRegList: optRegList
          };
        });
        if (logic) {
          const logicList = logic.split('');
          let flag = false;
          if (this.regTableList.length > 0 && logic.includes('|')) {
            const orStr = logicList.filter((item) => item == '|');
            const notStr = logicList.filter((item) => item == '|');
            if (notStr.length - 1 == orStr.length) {
              flag = true;
              this.lableForm.ruleCon = 1;
            }
          }
          if (!flag && logic.includes('&') && this.regTableList.length > 0) {
            const andStr = logicList.filter((item) => item == '&');
            const noAndStr = logicList.filter((item) => item != '&');
            if (noAndStr.length - 1 == andStr.length) {
              flag = true;
              this.lableForm.ruleCon = 2;
            }
          }
          if (!flag) {
            this.lableForm.ruleCon = 3;
            this.lableForm.logic = logic;
          }
        }
      }
    }
    this.getFildFList();
  },

  mounted() {},

  methods: {
    getFildFList(query) {
      this.loading = true;
      const p = {
        isPageQuery: true,
        page: 1,
        searchConditionList: query
          ? [{ fieldName: 'name', columnExp: 'like', value: query }]
          : [],
        limit: 20,
        columnList: [
          'comment',
          'created_at',
          'name',
          'remark',
          'samples',
          'id',
          'updated_at',
          'dc_table.name',
          'dc_data_source.url',
          'dc_data_source.name',
          'dc_data_source.business_system',
          'dc_table.comment',
          'dc_schema.name',
          'dc_level.name',
          'label_names.names',
          'manual',
          'dc_data_source.dc_data_source_type.name',
          'task_names.names',
          'dc_database.name'
        ]
      };
      postFiledList(p).then((res) => {
        this.testFiledList = (res.data?.rows || []).map((item) => {
          let nameStr = item.name || '--';
          if (item.dc_table) {
            nameStr = nameStr + '-' + item.dc_table.name;
          }
          if (item.dc_database) {
            nameStr = nameStr + '-' + item.dc_database.name;
          }
          if (item.dc_schema) {
            nameStr = nameStr + '-' + item.dc_schema.name;
          }
          if (item.dc_data_source) {
            nameStr = nameStr + '-' + item.dc_data_source.name;
          }
          return {
            name: nameStr,
            id: item.id
          };
        });
        this.loading = false;
      });
    },
    testFiledVal() {
      if (this.regTableList.length == 0) {
        this.$message.warning('请填写匹配规则');
        return;
      }
      for (let i = 0; i < this.regTableList.length; i++) {
        const vals = Object.values(this.regTableList[i]);
        if (vals.includes('') || vals.length == 0) {
          this.$message.warning('请填写完整的匹配规则');
          return;
        }
      }
      if (!this.testFiled) {
        this.$message.warning('请选择测试字段');
        return;
      }
      const p = {
        label: {
          id: this.testFiled,
          rule: {
            decision: {
              logic: ''
            }
          }
        },
        columnId: {
          id: this.testFiled
        }
      };
      p.label.rule.ruleConditions = this.regTableList.map((item, index) => {
        return {
          feature: item.feature,
          seq: index + 1,
          value: item.value,
          operator: item.operator
        };
      });
      if (this.lableForm.ruleCon === 1) {
        p.label.rule.decision.logic = this.regTableList
          .map((item, index) => index + 1)
          .join('|');
      } else if (this.lableForm.ruleCon === 2) {
        p.label.rule.decision.logic = this.regTableList
          .map((item, index) => index + 1)
          .join('&');
      } else {
        p.label.rule.decision.logic = this.lableForm.logic;
      }
      postFiledTest(p)
        .then((res) => {
          if (res?.data?.result === true) {
            this.$message.success('匹配成功');
          } else {
            this.$message.error('匹配失败');
          }
        })
        .catch((err) => this.$message.error(err.msg || '匹配失败'));
    },
    getFeatureName(fName) {
      for (let i = 0; i < this.featureList.length; i++) {
        const fVals = this.featureList[i].values;
        const info = fVals.find((item) => item.name == fName);
        if (info) {
          return this.featureList[i].name;
        }
      }
    },
    addRule() {
      this.regTableList.push({
        featureName: '',
        feature: '',
        operator: '',
        value: '',
        indicatorChildList: [],
        optRegList: []
      });
    },
    deleteRule(index) {
      this.regTableList.splice(index, 1);
    },
    changeVal(val, row) {
      const indicatorChildList =
        this.featureList.find((item) => item.name == val)?.values || [];
      if (row) {
        row.feature = '';
        row.value = '';
        row.operator = '';
        this.$set(row, 'indicatorChildList', indicatorChildList);
      }
      return indicatorChildList;
    },
    changeChildVal(val, row, list) {
      const indicatorChildList = row ? row?.indicatorChildList : list;
      const info = indicatorChildList.find((item) => item.name == val);
      const optRegList = (info?.options || []).map((item) => {
        return {
          title: this.featureOptions[item],
          name: item
        };
      });
      if (row) {
        row.operator = '';
        row.value = '';
        this.$set(row, 'optRegList', optRegList);
      }
      return optRegList;
    },
    changeReg(val, row) {
      row.value = '';
    },
    cancle() {
      this.$emit('cancel');
    },
    sure() {
      this.$refs.lableForm.validate((v) => {
        if (v) {
          const copyForm = deepCopy(this.lableForm);
          //序号seq
          const p = {
            name: copyForm.name,
            description: copyForm.description,
            parent: {
              id: copyForm.lableClassId
            },
            level: {
              id: copyForm.lableLeveld
            },
            rule: {
              decision: {
                logic: ''
              }
            }
          };
          p.rule.ruleConditions = this.regTableList.map((item, index) => {
            return {
              feature: item.feature,
              seq: index + 1,
              value: item.value,
              operator: item.operator
            };
          });
          if (copyForm.ruleCon === 1) {
            p.rule.decision.logic = this.regTableList
              .map((item, index) => index + 1)
              .join('|');
          } else if (copyForm.ruleCon === 2) {
            p.rule.decision.logic = this.regTableList
              .map((item, index) => index + 1)
              .join('&');
          } else {
            p.rule.decision.logic = copyForm.logic;
          }
          if (this.detailId && !this.isDetail) {
            p.id = this.detailId;
          }

          postFiledLabelAdd(p)
            .then((res) => {
              this.$message.success(res.msg || '操作成功');
              this.$emit('save');
            })
            .catch((err) => this.$message.error(err.msg || '操作失败'));
        }
      });
    },
    oneChange() {
      this.lableForm.startDate = '';
      this.lableForm.period = '';
      this.lableForm.periodStart = '';
      this.lableForm.periodType = '';
    },
    periodChange() {
      this.lableForm.periodType = '';
      this.lableForm.periodStart = '';
    }
  }
};
</script>
<style lang="less" scoped>
.class-level {
  padding: 20px;
  .tip-context {
    padding: 5px;
    background: #db6a15;
    color: #fff;
    border-radius: 4px;
    font-size: 12px;
  }
  .delete-btn {
    cursor: pointer;
    color: #f52c1e;
  }
  .add_btn {
    margin-top: 10px;
    cursor: pointer;
    background: #50b3f0;
    color: #fff;
    padding: 2px;
    text-align: center;
  }
  .rule-flex {
    float: left;
  }
  .cus_input {
    margin-left: 20px;
    width: 400px !important;
  }
}
</style>
