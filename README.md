镜像地址：registry-ops.qzkeji.cn/qzkj-ops/plate-form-fc:1.0.0


打包地址：https://ci.build.qzkeji.cn/#/builders/

## 数据模型Hasura嵌套

代码地址：

运行：npm run start:ce或者yarn run start:ce

打包：npm run build:ce后者yarn run build:ce

打包好后将dist文件夹中的数据拷贝到，该项目public中的hasura文件夹下

修改打包好的index.html文件，将<base href="/">改为<base href="./">

## 代码分支管理规范

### 原则

- 版本号使用 x.y.z 的形式
- 原则上，远程仓库只保留 dev 和 beta-<x.y.z> 这两种形式的分支

### 分支命名规范

- 主迭代开发，分支为 dev
- 主迭代发布后，分支为 beta-<x.y.z>
- 主迭代bug修复，分支为 beta-<x.y.z>-bugfix-<xxx>

### 分支命名最佳实践

以下示例中，以 1.0.0 版本为例。具体开发过程中，需要根据迭代节奏来修改版本号

#### 开启新迭代

在 dev 分支下修改 chart 号，不切新分支

#### 主迭代发布

从 dev 切出 beta-1.0.0

### 前端镜像替换步骤——简单操作

```shell
docker pull registry-ops.qzkeji.cn/qzkj-ops/rd-platform-f:1.0.0
rkscale --replicas=0 deployment.apps/rd-platform-f
docker tag registry-ops.qzkeji.cn/qzkj-ops/rd-platform-f:1.0.0 registry.common.svc.qzprod:8092/rd-platform-f:1.0.0
rkscale --replicas=1 deployment.apps/rd-platform-f
```

### 新拉的项目安装依赖后，要在package-lock.json中删除canvas依赖

#### 原因：
`CI`环境在`npm install`时，由于`canvas`依赖安装依靠`node-gyp`，而`node-gyp`依赖`python2.7`，而`python2.7`在`CI`环境不存在，导致`canvas`安装失败`，会导致打包非常慢，甚至打包失败。所以可以将`canvas`依赖删除
#### 解决办法：
如果有删除`package-lock.json`重新`npm install`的情况，需要先将`package-lock.json`文件中`canvas`依赖删除后再次`npm install`，这样会将`package-lock.json`文件中的`canvas`相关依赖全部删除`
