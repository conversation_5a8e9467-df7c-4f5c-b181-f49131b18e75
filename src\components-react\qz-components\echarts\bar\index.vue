<template>
  <div class="bar-charts">
    <qz-horizontal-bar v-if="type == 'horizontal'" :chartInfo="echartData" />
    <qz-vertical-bar v-else :chartInfo="echartData" />
  </div>
</template>

<script>
export default {
  props: ['props'],
  data() {
    return {
      echartData: {},
      type: 'horizontal'
    };
  },

  components: {},

  mounted() {
    const { chartType, echartSql, echartData } = this.props;
    this.type = chartType || 'horizontal';
    if (!echartData) {
      let xData = [29, 30, 36, 58, 198];
      let yData = [
        'http://nfc.cmpay.com',
        'http://************',
        'http://ipos.10086.cn',
        'http://nfc.cmpay.com',
        'http://www.dengluruomima003.com/login'
      ];

      if (this.type === 'horizontal') {
        this.echartData = {
          xData: xData,
          yData: yData
        };
      } else {
        this.echartData = {
          xData: yData,
          yData: xData
        };
      }
    } else {
      this.echartData = echartData || {};
    }
  },

  methods: {}
};
</script>
<style lang="less" scoped></style>
