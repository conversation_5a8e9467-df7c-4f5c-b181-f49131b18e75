// Only load these renders in development environment
if (process.env.NODE_ENV === 'development') {
  require('../store/table-store');
  require('./table');
  require('./toolbar-control');
  require('./filter-control');
  require('./filter-form');
  require('./filter-form-control');
  require('./form-items/input-ignore-case');
  require('./form-items/select');
  require('./form-items/radios');
  require('./form-items/checkboxes');
  require('./form-items/cascader');
  require('./column-toggler');
  require('./group-control');
  require('./operator-control');
  require('./group-selector');
  require('./view');
  require('../builder/datasource-builder');
}
