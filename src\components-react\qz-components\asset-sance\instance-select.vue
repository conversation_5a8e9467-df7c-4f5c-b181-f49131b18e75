<template>
  <div class="data-source">
    <el-form :model="sourceForm" size="small" inline>
      <el-form-item label="" prop="url">
        <el-input
          v-model="sourceForm.url"
          clearable
          placeholder="服务地址查询"
        />
      </el-form-item>
      <el-form-item label="" prop="business_system">
        <el-input
          v-model="sourceForm.business_system"
          clearable
          placeholder="业务系统查询"
        />
      </el-form-item>
      <el-form-item label="" prop="name">
        <el-input
          v-model="sourceForm.name"
          clearable
          placeholder="服务名称查询"
        />
      </el-form-item>
      <el-form-item label="" prop="status">
        <el-select v-model="sourceForm.status" clearable placeholder="勾选状态">
          <el-option label="已勾选" value="checked" />
        </el-select>
      </el-form-item>
      <!-- 后面三个条件待定 -->
      <!-- <el-form-item label="" prop="databaseName">
      <el-input v-model="sourceForm.databaseName" placeholder="数据库名称查询"/>
    </el-form-item>
    <el-form-item label="" prop="status">
      <el-select v-model="sourceForm.status" placeholder="勾选状态">
        <el-option label="已选中" value="1" />
        <el-option label="未选中" value="0" />
      </el-select>
    </el-form-item>
    <el-form-item label="" prop="filter">
      <el-checkbox v-model="sourceForm.filter">自动过滤系统库</el-checkbox>
    </el-form-item> -->
      <el-form-item label="" prop="">
        <el-button type="primary" @click="search">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="selection-info" v-if="selectedRows.length>0">
      <span>已选择{{ selectedRows.length }}/{{ maxSelectCount }}个数据服务</span>
      <span v-if="isMaxSelected" class="max-tip">(已达到最大选择数量)</span>
    </div>
    <qz-pro-table
      ref="treeTable"
      :dataSource="getTableData"
      style="width: 100%"
      @select="handleTableChange"
      @select-all="handleSecletAll"
      @expand-change="handleExpandChange"
      :row-key="(row) => row.id"
    >
      <qz-table-column
        type="selection"
        width="55"
        :selectable="isRowSelectable"
      />
      <qz-table-column type="expand">
        <template slot-scope="props">
          <div class="table-expand-content" v-loading="props.row.loading">
            <template v-if="props.row.childrenList.length === 0">
              <el-empty description="暂无数据"></el-empty>
            </template>
            <template v-else>
              <el-checkbox-group
                class="database-list"
                :key="getUuid()"
                v-model="props.row.children"
              >
                <el-checkbox
                  v-for="(item, index) in props.row.childrenList"
                  :label="
                    sendType === 'send'
                      ? `${item.id}@_@${item.name}`
                      : item.name
                  "
                  :key="`${item.name}@_@${index}`"
                >
                  {{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
              <el-pagination
                :key="getUuid()"
                class="pagenation"
                background
                layout="sizes,total,prev, pager, next"
                :page-sizes="[10, 20, 50]"
                :page-size="props.row.currentSize"
                :current-page="props.row.currentPage"
                @size-change="
                  ($event) => props.row.handleSizeChange($event, props.row)
                "
                @current-change="
                  ($event) => props.row.handleCurrentChange($event, props.row)
                "
                :total="props.row.total"
              >
              </el-pagination>
            </template>
          </div>
        </template>
      </qz-table-column>
      <qz-table-column prop="url" label="服务地址">
        <template slot-scope="{ row }">
          <span class="url" @click="showDetail(row)">{{
            row.url || '--'
          }}</span>
        </template>
      </qz-table-column>
      <qz-table-column prop="business_system" label="业务系统" />
      <qz-table-column prop="name" label="服务名称" />
    </qz-pro-table>
    <div class="text-right mt20">
      <el-button size="small" @click="cancle">取消</el-button>
      <el-button type="primary" size="small" @click="addSorceService"
        >添加</el-button
      >
    </div>
  </div>
</template>
<script>
import {
  postSourceList,
  postDataBaseList,
  postGetDatabase
} from '@/service/data-sance-service';
import { deepCopy } from '@/utils/deep-copy';
import { getUuid } from '@/utils/uuid';
export default {
  props: ['params'],
  data() {
    return {
      sourceForm: {},
      sendType: '',
      selectedRows: [],
      maxSelectCount: 5
    };
  },
  mounted() {
    this.sendType = this.params?.from || '';
    if (this.params?.list?.length > 0) {
      this.selectedRows = JSON.parse(JSON.stringify(this.params.list));
    }
  },
  computed: {
    isMaxSelected() {
      return this.selectedRows.length >= this.maxSelectCount;
      // ru
    },
    selectedServiceIds() {
      return this.selectedRows.map((item) => item.id);
    }
  },
  methods: {
    isRowSelectable(row) {
      const isSelected = this.selectedServiceIds.includes(row.id);
      return isSelected || !this.isMaxSelected;
    },
    getUuid,
    showDetail(row) {
      this.$DrawAlert({
        params: {
          info: row,
          callBack: (res) => {}
        },
        title: '详情',
        width: 60,
        componentObj: {
          component: () => import('./server-detail.vue')
        }
      });
    },
    search() {
      this.$refs.treeTable.reload();
    },
    cancle() {
      this.params.closeOutDrawer();
    },
    addSorceService() {
      //选中的
      const selectedTableData = deepCopy(
        this.$refs.treeTable.$refs.table.selection
      );
      // 表格
      const tableData = this.$refs.treeTable.$refs.table.data;
      const selectedTableDataIds = selectedTableData.map((item) => item.id);
      for (let i = 0; i < tableData.length; i++) {
        if (
          tableData[i].children.length > 0 &&
          !selectedTableDataIds.includes(tableData[i].id)
        ) {
          selectedTableData.push(tableData[i]);
        }
      }
      const queryList = this.params?.list || [];
      selectedTableData.forEach((item) => {
        const info = queryList.find((citem) => citem.id == item.id);
        if (info) {
          const concatChildren = item?.children?.concat(info.children);
          if (concatChildren.length > 0) {
            //处理不同格式的数据
            if (typeof concatChildren[0] == 'object') {
              try {
                item.children = [
                  ...new Map(
                    concatChildren.map((item) => [item.name, item])
                  ).values()
                ];
              } catch (e) {
                console.log(e);
              }
            } else {
              item.children = [...new Set(concatChildren)];
            }
          }
        }
      });
      this.params.callBack(selectedTableData);
      this.cancle();
    },
    handleTableChange(selected, row) {
      const rIndex = this.selectedRows.findIndex((citem) => citem.id == row.id);
      if (rIndex == -1) {
        this.selectedRows.push(row);
      } else {
        this.selectedRows.splice(rIndex, 1);
      }
      console.log(this.selectedRows);
    },
    handleSecletAll(section) {
      const currentTableData = this.$refs.treeTable.$refs.table.data;
      if (section.length === 0) {
        currentTableData.forEach((item) => {
          const rowIndex = this.selectedRows.findIndex(
            (citem) => citem.id == item.id
          );
          if (rowIndex != -1) {
            this.selectedRows.splice(rowIndex, 1);
          }
        });
      } else {
        this.$refs.treeTable.$refs.table.clearSelection();
        let canSelectCount=this.maxSelectCount-this.selectedRows.length;
        let selectedCount=0;
        currentTableData.forEach((item)=>{
          const isAlreadySelected=this.selectedRows.findIndex(citem=>citem.id==item.id)!==-1;
          if(!isAlreadySelected&&selectedCount<canSelectCount){
            this.selectedRows.push(item);
            this.$refs.treeTable.$refs.table.toggleRowSelection(item,true);
            selectedCount++;
          }else if(isAlreadySelected){
            this.$refs.treeTable.$refs.table.toggleRowSelection(item,true);
          }
        })
        if(selectedCount===0&&canSelectCount===0){
          this.$message.warning(`最多只能选择${this.maxSelectCount}个数据源`);
        }
        // section.forEach((item) => {
        //   if (
        //     this.selectedRows.findIndex((citem) => citem.id == item.id) == -1
        //   ) {
        //     this.selectedRows.push(item);
        //   }
        // });
      }
    },
    getTableData(pageInfo) {
      if (this.sourceForm.status == 'checked') {
        return this.handelTableData(
          this.selectedRows,
          this.selectedRows.length
        );
      }
      const searchConditionList = [];
      const params = {
        url: this.sourceForm.url,
        business_system: this.sourceForm.business_system,
        name: this.sourceForm.name
      };
      Object.keys(params).forEach((key) => {
        if (params[key]) {
          const sourceParams = {
            value: `%${params[key]}%`,
            fieldName: key,
            columnExp: 'like'
          };
          searchConditionList.push(sourceParams);
        }
      });
      if (this.sendType != 'sance') {
        //只查询已扫描的数据
        searchConditionList.push({
          fieldName: 'scan_type',
          columnExp: '=',
          value: '0'
        });
      }

      const p = {
        isPageQuery: true,
        page: pageInfo.page,
        searchConditionList: searchConditionList,
        limit: pageInfo.limit,
        columnList: [
          'created_at',
          'name',
          'id',
          'updated_at',
          'url',
          'name',
          'business_system',
          'dc_server_find_task.name',
          'database_count',
          'scan_type',
          'connect_type',
          'schema_count',
          'table_count',
          'column_count',
          'dc_data_source_type.name'
        ],
        sortList: [{ fieldName: 'created_at', sortExp: 'desc' }]
      };
      return postSourceList(p).then((res) => {
        const totalCount = res.data.count;
        return this.handelTableData(res.data?.rows || [], totalCount);
      });
    },
    handelTableData(accpetData, totalCount) {
      setTimeout(() => {
        this.setTableSeclectedRows();
      }, 0);
      return new Promise((resolve, reject) => {
        resolve({
          data: {
            rows: accpetData.map((item) => {
              return {
                ...item,
                children: [],
                childrenList: [],
                loading: false,
                currentPage: 1,
                currentSize: 10,
                handleSizeChange: (val, row) => {
                  row.currentSize = val;
                  row.currentPage = 1;
                  this.handleExpandChange(row, row, {
                    page: row.currentPage,
                    limit: val
                  });
                },
                handleCurrentChange: (val, row) => {
                  row.currentPage = val;
                  this.handleExpandChange(row, row, {
                    page: val,
                    limit: row.currentSize
                  });
                }
              };
            }),
            totalCount: totalCount
          }
        });
      });
    },
    setTableSeclectedRows() {
      this.$nextTick(() => {
        const tableData = this.$refs.treeTable.$refs.table.data;
        tableData.forEach((item) => {
          const rowIndex = this.selectedRows.findIndex(
            (citem) => citem.id == item.id
          );
          if (rowIndex != -1) {
            this.$refs.treeTable.$refs.table.toggleRowSelection(item, true);
          }
        });
      });
    },
    handleExpandChange(row, isExpandRows, pageInfo = { page: 1, limit: 10 }) {
      row.loading = true;
      let p = {};
      if (this.sendType == 'send') {
        p = {
          page: pageInfo.page,
          limit: pageInfo.limit,
          searchConditionList: [
            {
              fieldName: 'source_id',
              columnExp: '=',
              value: row.id
            }
          ],
          columnList: ['name', 'id']
        };
      } else {
        p = {
          id: row.id,
          page: pageInfo.page - 1,
          size: pageInfo.limit
        };
      }
      if (this.sendType == 'class') {
        p.type = 'local';
      }
      const fn = this.sendType == 'send' ? postDataBaseList : postGetDatabase;
      fn(p)
        .then((res) => {
          row.childrenList = res?.data?.rows || [];
          row.total = res?.data?.count || 0;
          row.loading = false;
          if (row.childrenList.length == 0) {
            this.$message.warning('当前数据源服务未进行扫描或扫描结果为空');
          }
        })
        .catch((err) => {
          row.loading = false;
          this.$message.error(err.msg || '获取数据失败');
        });
    }
  }
};
</script>
<style lang="less" scoped>
.data-source {
  padding: 20px;
  .url {
    color: #4a97eb;
    cursor: pointer;
  }
  .max-tip{
      color: #666666;
    }
  .table-expand-content {
    position: relative;
    min-height: 60px;
    padding: 0px 10px 0px 100px;
    /deep/ .el-empty {
      height: 60px;
      .el-empty__image {
        height: 40px;
      }
    }
    .database-list {
      display: flex;
      flex-direction: column;
    }
    .pagenation {
      text-align: right;
      right: 0;
    }
  }
}
</style>
