<template>
  <div class="assets-task-edit" v-loading="loading">
    <div class="pd20">
      <el-form
        ref="form"
        :model="newTask"
        :rules="rules"
        size="small"
        class="task-edit-form"
        label-position="left"
        label-width="100px"
        :disabled="isDetail"
      >
        <!-- 任务名称 -->
        <el-form-item label="任务名称：" prop="name">
          <el-input
            v-model="newTask.name"
            maxlength="20"
            show-word-limit
            placeholder="请填写任务名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="任务描述：" prop="description">
          <el-input
            v-model="newTask.description"
            placeholder="请填写任务描述"
          ></el-input>
        </el-form-item>
        <!-- 扫描范围配置 -->
        <div class="mb20 pb0">
          <div class="text-right">
            <span class="tip"
              >仅支持选择资产-数据服务清单中连接状态为“可连接”的服务进行扫描</span
            >
            <el-button
              class="ml20"
              type="primary"
              size="small"
              @click="addServer"
            >
              添加数据服务
            </el-button>
            <el-button v-if="detailId" @click="deleteServer">删除</el-button>
          </div>
          <!-- 扫描模式 -->
          <div>
            <qz-pro-table
              v-if="!detailId || isCopy"
              class="mb20"
              ref="serverTable"
              border
              key="serverTable"
              :data-source="serverTableList"
              @select="selectHandle"
              @select-all="selectHandle"
            >
              <qz-table-column type="selection" width="45"></qz-table-column>
              <qz-table-column
                prop="url"
                label="服务地址"
                min-width="90"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span class="url" @click="showDetail(row)">{{
                    row.url
                  }}</span>
                </template>
              </qz-table-column>
              <qz-table-column
                prop="dataBase"
                label="库"
                min-width="140"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span>{{ getDateBase(row) }}</span>
                </template>
              </qz-table-column>
              <qz-table-column
                prop="business_system"
                label="业务系统"
                min-width="80"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span>{{ row.business_system || '--' }}</span>
                </template>
              </qz-table-column>
              <qz-table-column
                prop="connectivity"
                label="连接性"
                min-width="70"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span>{{ row.connectivity || '请点击连接' }}</span>
                </template>
              </qz-table-column>
              <qz-table-column
                fixed="right"
                prop="name"
                label="操作"
                min-width="90"
              >
                <template slot-scope="scope">
                  <div>
                    <el-button
                      type="text"
                      size="normal"
                      class="color-main"
                      @click="connectRow(scope.row)"
                    >
                      连接
                    </el-button>
                    <el-button
                      type="text"
                      size="normal"
                      @click="delInstance(scope.row)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
              </qz-table-column>
            </qz-pro-table>
            <qz-pro-table
              v-if="detailId && !isCopy"
              class="mb20"
              ref="serverTableRef"
              key="serverTableRef"
              border
              :data-source="serverTableList"
            >
              <qz-table-column
                prop="name"
                label="服务名称"
                min-width="90"
                show-overflow-tooltip
              >
              </qz-table-column>
              <qz-table-column
                prop="hostport"
                label="服务信息"
                min-width="90"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span>{{ getServerInfo(row) }}</span>
                </template>
              </qz-table-column>
              <qz-table-column
                prop="username"
                label="数据库账号"
                min-width="90"
                show-overflow-tooltip
              >
              </qz-table-column>

              <qz-table-column
                prop="database"
                label="扫描范围"
                min-width="90"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span>{{ getSanRange(row) }}</span>
                </template>
              </qz-table-column>
              <qz-table-column
                prop="business_system"
                label="业务系统"
                min-width="90"
                show-overflow-tooltip
              >
              </qz-table-column>
              <qz-table-column
                prop="connect_type"
                label="连接性"
                min-width="90"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <span>{{
                    row.connect_type === 0 ? '可连接' : '不可连接'
                  }}</span>
                </template>
              </qz-table-column>
              <qz-table-column
                fixed="right"
                prop=""
                label="操作"
                min-width="120"
              >
                <template slot-scope="scope">
                  <div>
                    <el-button
                      v-if="sanceStatus == '扫描失败'"
                      type="text"
                      size="normal"
                      class="color-main"
                      @click="connectRow(scope.row)"
                    >
                      连接
                    </el-button>
                    <el-button
                      type="text"
                      size="normal"
                      class="color-main"
                      @click="showDetail(scope.row)"
                    >
                      查看详情
                    </el-button>
                    <el-button
                      :disabled="isDetail"
                      type="text"
                      size="normal"
                      @click="delInstance(scope.row)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
              </qz-table-column>
            </qz-pro-table>
            <el-collapse-transition>
              <div class="border scan-executionType">
                <el-form-item
                  label="执行方式："
                  prop="executionType"
                  class="w100"
                >
                  <el-radio-group
                    v-model="newTask.executionType"
                    @change="oneChange"
                  >
                    <el-radio label="IMMEDIATE">立即执行</el-radio>
                    <el-radio label="TIME">定时执行</el-radio>
                    <el-radio label="PERIOD">周期执行</el-radio>
                  </el-radio-group>
                  <div
                    class="bg-gray"
                    v-if="newTask.executionType != 'IMMEDIATE'"
                  >
                    <el-form-item
                      class="is-required"
                      label="执行时间:"
                      prop="startDate"
                      label-width="90px"
                      v-if="newTask.executionType == 'TIME'"
                    >
                      <el-date-picker
                        v-model="newTask.startDate"
                        type="datetime"
                        value-format="timestamp"
                        placeholder="选择日期时间"
                        :picker-options="expireTimeOption"
                      ></el-date-picker>
                    </el-form-item>
                    <el-form-item
                      label="周期选择:"
                      prop="period"
                      label-width="90px"
                      class="is-required"
                      v-if="newTask.executionType == 'PERIOD'"
                    >
                      <el-select
                        placeholder="频次"
                        class="period-type-picker"
                        size="small"
                        v-model="newTask.period"
                        @change="periodChange"
                      >
                        <el-option value="DAY" label="每日"></el-option>
                        <el-option value="WEEK" label="每周"></el-option>
                        <el-option value="MONTH" label="每月"></el-option>
                      </el-select>
                      <el-select
                        placeholder="日期"
                        class="period-type-picker"
                        size="small"
                        v-model="newTask.periodType"
                        v-if="newTask.period != 'DAY'"
                      >
                        <template v-if="newTask.period == 'WEEK'">
                          <el-option value="1" label="周一"></el-option>
                          <el-option value="2" label="周二"></el-option>
                          <el-option value="3" label="周三"></el-option>
                          <el-option value="4" label="周四"></el-option>
                          <el-option value="5" label="周五"></el-option>
                          <el-option value="6" label="周六"></el-option>
                          <el-option value="7" label="周日"></el-option>
                        </template>
                        <template v-if="newTask.period == 'MONTH'">
                          <el-option
                            :value="item.value"
                            :label="item.label"
                            v-for="item in MONTH_LIST"
                            :key="item.value"
                          ></el-option>
                        </template>
                      </el-select>
                      <el-time-picker
                        v-else
                        class="time-range-picker"
                        v-model="newTask.periodStart"
                        placeholder="任意时间点"
                        format="HH:mm"
                        value-format="timestamp"
                      ></el-time-picker>
                    </el-form-item>
                  </div>
                </el-form-item>
                <el-form-item label="分类分级：" prop="autoLabel">
                  <el-checkbox v-model="newTask.autoLabel"
                    >资产扫描后自动开始分类分级</el-checkbox
                  >
                </el-form-item>
                <template v-if="newTask.autoLabel">
                  <el-form-item label="分级策略:" prop="type">
                    <el-radio-group v-model="newTask.type" @change="changeVal">
                      <el-radio
                        v-for="item in classList"
                        :label="item.value"
                        :key="item.value"
                        >{{ item.label }}</el-radio
                      >
                    </el-radio-group>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content=""
                      placement="top"
                      style="position: absolute; left: -26px; top: 8px"
                    >
                      <em class="el-icon-warning el-alert--info is-light"></em>
                      <div
                        slot="content"
                        v-html="
                          '正则分级是根据当前的标签规则对数据进行分类分级，AI分级是接入AI模型进行分类分级，混合模式是同时使用正则分级和AI分级，准确率更高，建议选择混合模式。'
                        "
                      ></div>
                    </el-tooltip>
                  </el-form-item>
                  <template
                    v-if="newTask.type == 'AI' || newTask.type == 'MIXED'"
                  >
                    <el-form-item
                      :label="
                        newTask.type == 'AI' ? '选择AI模型' : '选择混合模型'
                      "
                      prop="model"
                    >
                      <el-select
                        class="width-full"
                        v-model="newTask.model"
                        :placeholder="
                          newTask.type == 'AI' ? '选择AI模型' : '选择混合模型'
                        "
                      >
                        <el-option
                          v-for="(item, key) in aiList"
                          :key="item.value"
                          :label="item.name"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="
                        newTask.type == 'AI' ? '选择AI策略' : '混合模型策略'
                      "
                      prop="skipSystem"
                    >
                      <el-checkbox v-model="newTask.skipSystem"
                        >仅对业务表进行分类分级</el-checkbox
                      >
                    </el-form-item>
                  </template>
                  <el-form-item label="分级范围:" prop="increment">
                    <el-radio-group v-model="newTask.increment">
                      <el-radio :label="false">全量分级</el-radio>
                      <el-radio :label="true">增量分级</el-radio>
                    </el-radio-group>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content=""
                      placement="top"
                      style="position: absolute; left: -26px; top: 8px"
                    >
                      <em class="el-icon-warning el-alert--info is-light"></em>
                      <div
                        slot="content"
                        v-html="
                          '增量分级是对该服务没有进行过分类分级的数据进行分类分级。'
                        "
                      ></div>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item label="覆盖策略:" prop="coveyLock">
                    <el-checkbox v-model="newTask.coveyLock"
                      >覆盖锁定数据</el-checkbox
                    >
                  </el-form-item>
                </template>
                <div class="scan-executionType-item">
                  <el-form-item label="获取样例数：" label-width="140px">
                    <el-input-number
                      v-model="newTask.samplingProperties.limit"
                      placeholder="请填写获取样例数"
                      type="number"
                      :min="1"
                      :max="100"
                    ></el-input-number>
                  </el-form-item>
                  <el-form-item label="并发数：" label-width="90px">
                    <el-input-number
                      v-model="newTask.samplingProperties.concurrency"
                      placeholder="请填写并发数"
                      type="number"
                      :min="1"
                      :max="5"
                    ></el-input-number>
                  </el-form-item>
                  <el-form-item label="采样模式：" label-width="90px">
                    <el-select
                      v-model="newTask.samplingProperties.sampleMode"
                      placeholder="请选择采样模式"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in sampleMode"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      ></el-option>
                    </el-select>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content=""
                      placement="top"
                      style="position: absolute; left: -25px; top: 8px"
                    >
                      <em class="el-icon-warning el-alert--info is-light"></em>
                      <div
                        slot="content"
                        v-html="
                          '倒序采样和随机采样方式仅支持postgres、Db2、dm、greenplum、impala、kingbase、Mysql、Oracle数据库，若所选的扫描对象中有其他库，则其他库仅支持使用正序采样。倒序采样和随机采样可能会导致扫描时间过长，请在空闲时间段使用'
                        "
                      ></div>
                    </el-tooltip>
                  </el-form-item>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </el-form>
    </div>
    <div class="text-right" v-if="!isDetail">
      <el-button @click="cancle">取消</el-button>
      <el-button :disabled="loading" type="primary" @click="sure"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
import MONTH_LIST from '@/constant/common-constants';
import {
  postSourceTest,
  postSourceSan,
  postTaskDetail,
  postSourceList
} from '@/service/data-sance-service';
import { postAiList } from '@/service/class-level-service';
import { deepCopy } from '@/utils/deep-copy';
import { handleCron, timestampToUTC8 } from '@/utils/string-utils';
import { Row } from 'amis-core/lib/store/table';
export default {
  props: ['props'],
  data() {
    return {
      MONTH_LIST,
      newTask: {
        name: '',
        description: '',
        startDate: '',
        periodStart: '',
        periodType: '',
        samplingProperties: {
          limit: 10,
          concurrency: 4,
          sampleMode: 'ASC'
        },
        serverList: [],
        executionType: 'IMMEDIATE',
        period: '',
        autoLabel: false,
        type: 'REGEX',
        model: '',
        increment: false,
        skipSystem: false,
        coveyLock: false
      },
      classList: [
        { label: '正则分级', value: 'REGEX' },
        { label: 'AI分级', value: 'AI' },
        { label: '混合模式', value: 'MIXED' }
      ],
      aiList: [],
      selectInstance: [],
      selectSelfIds: [],
      instanceConfigDrawer: false,
      instanceAddDrawer: false,
      testContentLoading: false,
      rules: {
        name: [{ required: true, message: '请填写任务名称', trigger: 'blur' }],
        startDate: [
          { required: true, message: '请选择执行时间', trigger: 'blur' }
        ],
        period: [
          { required: true, message: '请选择周期时间', trigger: 'blur' }
        ],
        executionType: [
          { required: true, message: '请选择执行方式', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择分级策略', trigger: 'change' }
        ],
        increment: [
          { required: true, message: '请选择分级范围', trigger: 'change' }
        ],
        model: [{ required: true, message: '请选择模型', trigger: 'change' }]
      },
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        }
      },
      flag: true,
      activeItemIndex: 0,
      taskInfo: {},
      serverTableList: [],
      detailId: '',
      sampleMode: [
        { label: '正序采样', value: 'ASC' },
        { label: '倒序采样', value: 'DESC' },
        { label: '随机采样', value: 'RANDOM' }
      ],
      isCopy: false,
      isDetail: false,
      loading: false,
      sanceStatus: ''
    };
  },
  computed: {
    operateInstanceDisadble() {
      if (this.newTask.serverList?.length === 0) {
        this.selectSelfIds = [];
      }
      return this.selectSelfIds?.length === 0;
    }
  },
  watch: {},
  async created() {
    const aiParams = {
      isPageQuery: true,
      page: 1,
      limit: 10000,
      columnList: ['name', 'id', 'key']
    };
    const aiRes = await postAiList(aiParams);
    this.aiList = aiRes?.data?.rows || [];
  },
  mounted() {
    //如果是从数据服务跳转
    if (this.props?.data?.jump_type == 'serve_list') {
      const info = {
        data_sources: [{ id: this.props?.data?.id }]
      };
      this.updateData(info);
    } else {
      // if (!this.props?.data?.id) return;
      // this.detailId = this.props?.data?.id;
      this.detailId=37;
      if (this.props?.data?.draw_type == 'copy') {
        this.isCopy = true;
      }
      if (this.props?.data?.draw_type == 'detail') {
        this.isDetail = true;
      }
      this.sanceStatus = this.props?.data?.state || '';
      const params = {
        searchConditionList: [
          { fieldName: 'id', columnExp: '=', value: this.detailId }
        ],
        columnList: [
          'skip_system',
          'task_flag',
          'last_success_at',
          'start_date',
          'id',
          'name',
          'description',
          'state',
          'created_at',
          'complete_data_source_count',
          'cron',
          'concurrency',
          'start_at',
          'execution_msg',
          'execution_type',
          'data_sources',
          'updated_at',
          'sample_count',
          'end_at',
          'progress',
          'sample_mode',
          'ext_config'
        ]
      };
      postTaskDetail(params).then((res) => {
        const detail = res.data || {};
        //这个需要判断是不是复制
        if (this.isCopy) {
          this.newTask.name = `副本-${detail.name}`;
        } else {
          this.newTask.name = detail.name;
        }

        this.newTask.description = detail.description;
        if (detail.execution_type === 1) {
          this.newTask.executionType = 'TIME';
          this.newTask.startDate = new Date(detail.start_date).valueOf();
        } else if (detail.execution_type === 2) {
          this.newTask.executionType = 'PERIOD';
          this.handleCronToTime(detail.cron);
        } else {
          this.newTask.executionType = 'IMMEDIATE';
        }
        this.newTask.autoLabel = detail?.task_flag === 1;
        this.newTask.samplingProperties = {
          limit: detail.sample_count,
          concurrency: detail.concurrency,
          sampleMode: this.sampleMode[detail.sample_mode - 1].value
        };
        if (detail.ext_config) {
          const extConfig = JSON.parse(detail.ext_config)?.classification || {};
          this.newTask.type = extConfig.type;
          this.newTask.increment = extConfig.increment || false;
          this.newTask.model = extConfig?.model?.id || '';
          this.newTask.skipSystem = extConfig.skipSystem || false;
          this.newTask.coveyLock = extConfig?.coveyLock || false;
        }
        this.updateData(detail);
      });
    }
  },
  methods: {
    changeVal(val) {
      this.newTask.skipSystem = false;
      this.newTask.model = '';
    },
    async updateData(detail) {
      const dataSources = detail?.data_sources || [];
      for (const item of dataSources) {
        try {
          const p = {
            isPageQuery: true,
            page: 1,
            searchConditionList: [
              {
                value: item.id,
                fieldName: 'id',
                columnExp: '='
              }
            ],
            limit: 10,
            columnList: [
              'name',
              'id',
              'url',
              'business_system',
              'connect_type',
              'port',
              'host',
              'username'
            ]
          };
          const res = await postSourceList(p);
          const resData = res?.data?.rows[0] || {};
          if (Object.keys(resData).length === 0) {
            return;
          }
          const {
            name,
            url,
            business_system,
            connect_type,
            host,
            port,
            username
          } = resData;
          if (!name) {
            item.name = '服务已清理';
          } else {
            item.name = name || '--';
          }
          item.url = url || '--';
          item.business_system = business_system || '--';
          item.connect_type = connect_type;
          item.hostport = `${host || '--'}:${port || '--'}`;
          item.username = username || '--';
          item.children = item.databases;
          this.serverTableList.push(item);
        } catch (error) {
          console.error('请求数据时出错:', error);
        }
      }
    },
    getSanRange(row) {
      if (row.name == '服务已清理') return '--';
      return this.handleServer(row);
    },
    deleteServer() {
      this.serverTableList = [];
    },
    handleCronToTime(cron) {
      if (!cron) return;
      const corns = cron.split(' ');
      //每周周几
      if (cron.startsWith('0 0 0 ? *')) {
        const weekDay = corns[5];
        this.newTask.period = 'WEEK';
        this.newTask.periodType = (weekDay - 1).toString();
      } else if (cron.endsWith('* ? *')) {
        //每月几号
        const monthDay = corns[3];
        const day = monthDay == 'L' ? 'L' : monthDay;
        this.newTask.period = 'MONTH';
        this.newTask.periodType = day;
      } else {
        const h = corns[2];
        const m = corns[1];
        const s = corns[0];
        const date = new Date();
        const year = date.getFullYear();
        const month = date.getMonth();
        const day = date.getDay();
        const fixDay = `${year}-${month}-${day} ${h}:${m}:${s}`;
        const timestamp = new Date(fixDay).valueOf();
        this.newTask.period = 'DAY';
        this.newTask.periodStart = timestamp;
      }
    },
    showDetail(row) {
      this.$DrawAlert({
        params: {
          info: row,
          callBack: (res) => {}
        },
        title: '详情',
        width: 60,
        componentObj: {
          component: () => import('./server-detail.vue')
        }
      });
    },
    cancle() {
      this.$emit('cancel');
    },
    sure() {
      this.$refs['form'].validate((v) => {
        if (v) {
          this.loading = true;
          const p = {
            name: this.newTask.name,
            description: this.newTask.description,
            executionType: this.newTask.executionType,
            autoLabel: this.newTask.autoLabel || false,
            ...this.newTask.samplingProperties,
            dataSources: this.serverTableList.map((item) => {
              //兼容两种情况
              const databasesList =
                item?.children?.filter((item) => {
                  if (typeof item == 'object') return item.name;
                  if (typeof item == 'string') return item;
                }) || [];
              let databasesVals = [];
              databasesVals = databasesList.map((item) => {
                //兼容两种情况
                if (typeof item == 'object') return { name: item.name };
                if (typeof item == 'string') return { name: item };
              });
              return {
                id: item.id,
                databases: databasesVals
              };
            })
          };
          if (this.newTask.autoLabel) {
            p.classification = {
              type: this.newTask.type,
              model: {
                id: this.newTask.model
              },
              skipSystem: this.newTask.skipSystem,
              increment: this.newTask.increment,
              coveyLock: this.newTask.coveyLock
            };
          }
          if (this.newTask.executionType == 'TIME') {
            p.startDate = timestampToUTC8(this.newTask.startDate);
          }
          if (this.newTask.executionType == 'PERIOD') {
            const timeCycl = {
              cycl: this.newTask.period,
              date: this.newTask.periodStart,
              week: this.newTask.periodType,
              month: this.newTask.periodType
            };
            p.cron = handleCron(timeCycl);
          }
          if (this.detailId && !this.isCopy) {
            p.id = this.detailId;
          }
          postSourceSan(p)
            .then((res) => {
              this.$message.success('操作成功');
              this.loading = false;
              this.$emit('save');
              //调用刷新amis刷新表格
            })
            .catch((err) => {
              this.$message.error(err?.msg || '操作失败');
              this.loading = false;
            });
        }
      });
    },
    getDateBase(row) {
      return this.handleServer(row);
    },
    handleServer(row) {
      if (row?.children?.length == 0 || !row.children) {
        return '全部';
      } else {
        const strList = [];
        row?.children?.forEach((item) => {
          if (item && typeof item == 'string') {
            strList.push(item);
          }
          if (item && typeof item == 'object') {
            item?.name && strList.push(item?.name);
          }
        });
        if (strList.length == 0) return '全部';
        else return strList.join();
      }
    },
    getServerInfo(row) {
      if (row.hostport) return row.hostport;
      else if (row.url) return row.url;
      else return '--';
    },
    connectRow(row) {
      postSourceTest({ id: row.id })
        .then((res) => {
          this.$set(row, 'connectivity', '可连接');
        })
        .catch((err) => {
          this.$set(row, 'connectivity', '不可连接');
          this.$message.error(err.msg || '不可连接');
        });
    },
    periodChange() {
      this.newTask.periodType = '';
      this.newTask.periodStart = '';
    },
    cancel() {},
    addServer() {
      this.$DrawAlert({
        params: {
          from: 'sance',
          list: this.serverTableList,
          callBack: (res) => {
            const copyServerTableList = deepCopy(this.serverTableList);
            res.forEach((item) => {
              const index = copyServerTableList.findIndex(
                (citem) => citem.id == item.id
              );
              if (index != -1) {
                copyServerTableList[index] = item;
              } else {
                copyServerTableList.push(item);
              }
            });
            this.serverTableList = copyServerTableList;
          }
        },
        title: '添加数据服务',
        width: 70,
        componentObj: {
          component: () => import('./instance-select.vue')
        }
      });
    },
    checkPort(rule, value, callback) {
      const reg = /^\d+$/;
      if (!value) {
        callback(new Error('请配置服务的端口号'));
      } else if (reg.test(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的端口号'));
      }
    },
    checkName(rule, value, callback) {
      if (!value) {
        callback(new Error('请配置服务的名称'));
      } else {
        callback();
      }
    },
    delInstance(row) {
      const index = this.serverTableList.findIndex((item) => item.id == row.id);
      this.serverTableList.splice(index, 1);
    },
    // 选择任务中某个实例进行测试连接或删除
    selectHandle(selection) {
      this.selectInstance = [];
      this.selectSelfIds = [];
      selection.forEach((item) => {
        this.selectInstance.push(item);
        this.selectSelfIds.push(item.selfId);
      });
    },
    // 单次执行时间选择
    oneChange() {
      this.newTask.startDate = '';
      this.newTask.period = '';
      this.newTask.periodStart = '';
      this.newTask.periodType = '';
    }
  }
};
</script>

<style lang="less" scoped>
.assets-task-edit {
  height: 100%;
  .task-edit-form {
    .el-form-item {
      // width: 49%;
      .el-form-item__label {
        font-size: 14px;
        color: #333333;
        text-align: right;
        font-weight: 400;
      }
      /deep/.el-form-item__content {
        width: calc(100% - 150px);
      }
    }
    .scan-executionType {
      cursor: pointer;
      font-size: 14px;
      .qz-iconfont {
        font-size: 11px;
        margin-left: 2px;
      }
    }
    .qz-pro-table {
      margin-top: 0px;
    }
    .scan-executionType {
      .scan-executionType-item {
        display: flex;
        justify-content: space-between;
        padding: 20px 0 0;
        > .el-form-item {
          flex: 2;
          &:last-child {
            flex: 3;
          }
        }
        // .el-form-item__content {
        //   width: calc(100% - 140px);
        // }
      }
    }
  }
  .picker-no-footer {
    .el-picker-panel__footer,
    .el-time-panel__footer {
      display: none;
    }
  }

  [contenteditable='true'] {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px;
    outline: none;
    min-width: 100px;
    height: 40px;
    box-sizing: border-box;
    vertical-align: middle;
    border-bottom: 1px solid #00c350;
  }
  .db-tree {
    height: 100%;
    overflow-y: auto;
    border-right: 1px solid #ddd;
  }
  .background-items {
    padding-top: 23px;
    padding-bottom: 10px;
    background: #f9f9f9;
    border-radius: 4px;
  }

  /deep/.el-select .el-tag__close.el-icon-close {
    position: relative;
    top: 1px;
    right: -5px;
  }
  /deep/.el-select__tags-text {
    max-width: 100%;
  }
  .scan-scope-box {
    padding: 23px 23px;
    width: 100%;
    margin-bottom: 20px;
    .el-radio {
      // width: 120px;
    }
  }
  .selected-data {
    position: absolute;
    right: -15px;
    top: 8px;
    transform: translateX(100%);
    font-size: 12px;
    cursor: pointer;
    text-decoration: underline;
  }
  .tip {
    color: #db920a;
  }
  .url {
    color: #4a97eb;
    cursor: pointer;
  }
}
</style>
