@Color_main_1: #4a97eb;

*,
*::before,
*::after {
  box-sizing: border-box;
}

::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 3px;
}

.main-content {
  background-color: #fff;
  padding: 20px;
  border: 1px solid rgb(236, 236, 238);
  min-width: 1100px;
}

.overflow-content {
  min-width: 1100px;
  height: 100%;
}

.align-center {
  text-align: center;
}

.align-right {
  text-align: right !important;
}

.text-right {
  text-align: right;
}

.m0 {
  margin: 0 !important;
}

.m10 {
  margin: 10px !important;
}

.m20 {
  margin: 20px !important;
}

.mt0{
  margin-top: 0px !important;
}

.mt5 {
  margin-top: 5px !important;
}

.mt10 {
  margin-top: 10px !important;
}

.mt15 {
  margin-top: 15px !important;
}

.mt20 {
  margin-top: 20px !important;
}

.mr0 {
  margin-right: 0 !important;
}

.mr5 {
  margin-right: 5px !important;
}

.mr10 {
  margin-right: 10px !important;
}

.mr15 {
  margin-right: 15px !important;
}

.mr20 {
  margin-right: 20px !important;
}

.mb5 {
  margin-bottom: 5px !important;
}

.mb10 {
  margin-bottom: 10px !important;
}

.mb20 {
  margin-bottom: 20px !important;
}

.ml0 {
  margin-left: 0px !important;
}

.ml5 {
  margin-left: 5px !important;
}

.ml10 {
  margin-left: 10px !important;
}

.ml15 {
  margin-left: 15px !important;
}

.ml20 {
  margin-left: 20px !important;
}

.ml40 {
  margin-left: 40px !important;
}

.font14 {
  font-size: 14px !important;
}

.font16 {
  font-size: 16px !important;
}

.font18 {
  font-size: 18px !important;
}

.qz-form {
  // width: 1080px;
  margin: 0 auto;
}

.qz-el-icon {
  font-size: 18px;
  color: @Color_main_1;
  cursor: pointer;

  &+.qz-el-icon {
    margin-left: 10px;
  }
}

.color-main {
  color: @Color_main_1;
}

.color-danger {
  color: #f55959 !important;
}

.color-warning {
  color: #ea8a2f !important;
}

.form-item {
  display: flex;
  align-items: center;
  font-family: PingFangSC-Regular, Avenir, Helvetica, Arial, sans-serif;
  font-size: 14px;

  .item__label {
    flex: none;
    color: #2e3444;
  }

  .item__content {
    flex: auto;
    display: flex;
    align-items: center;
    color: #666;
    overflow: hidden;
  }
}

.split-line {
  border-top: 1px dashed #d3d4d5;
  margin-bottom: 20px;
}

/* 登录标题 字体 */
@font-face {
  font-family: PangMenZhengDao;
  src: url('../font/PangMenZhengDao.ttf') format('truetype');
}

// 折叠文字溢出隐藏
.text-hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-link {
  font-family: PingFangSC-Regular, Avenir, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: @Color_main_1;
  cursor: pointer;
  text-decoration: none;

  &:focus {
    outline: none;
  }

  +.action-link {
    margin-left: 10px;
  }

  &.disabled {
    color: #ddd;
    cursor: not-allowed;
  }

  &.danger {
    color: #f55959;
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.align-items-center {
  align-items: center;
}

.space-between {
  justify-content: space-between;
}

.space-around {
  justify-content: space-around;
}

.flex-end {
  justify-content: flex-end;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

.full-width {
  width: 100% !important;
}

.hidden {
  display: none;
}


.date-picker-no-data span {
  border: 1px solid #ddd;

  &::before {
    color: #ddd;
    content: '';
    border-left: 1px solid #ddd;
    width: 100%;
    height: 100%;
    position: absolute;
    transform: rotate(46deg);
    top: 8px;
    right: -8px;
  }
}

.detail-content {
  &__header {
    background: #ffffff;
    padding: 20px;

    &__title {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0px;
      color: #ea8a2f;
      padding-bottom: 15px;
      border-bottom: 1px solid #e4e7ed;
      margin-bottom: 20px;
    }
  }

  .el-tabs--border-card {
    box-shadow: none;
  }

  &__body {
    background: #0a0606;
    padding: 20px;
    margin-top: 20px;
  }
}

.left-input {
  margin-right: -1px;

  input {
    border-radius: 3px 0 0 3px !important;
  }
}

.right-input {
  input {
    border-radius: 0 3px 3px 0 !important;
  }
}

.level-color {
  width: 44px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 3px;
  color: #fff;
  display: inline-block;
  font-size: 12px;

  &.level-0 {
    background-color: #4ca474;
  }

  &.level-1 {
    background-color: #dece56;
  }

  &.level-2 {
    background-color: #eeb04f;
  }

  &.level-3 {
    background-color: #e3645f;
  }

  &.level-4 {
    background-color: #cd332c;
  }
}

.event-detail__dialog {
  z-index: 3000 !important;

  .qz-alert__body {
    position: relative;

    & .desensitize-btn {
      position: absolute;
      top: 17px;
      right: 45px;
      z-index: 2;
    }
  }
}

.qz-overview {
  padding: 10px;
  background: #ffffff;
  height: 345px;
  margin-bottom: 10px;

  &__title {
    font-family: PingFangSC-Semibold;
    font-size: 16px;
    font-weight: normal;
    color: #303133;
    line-height: 24px;
  }

  & .horizontal-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 330px !important;
  }

  &__content {
    height: 340px;
    overflow: auto;
  }
}

.trend {
  padding: 10px;
  background: #ffffff;
  height: 236px;

  .block-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.circle {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 8px;

  &.workorder-circle_-1 {
    background: #909399;
  }

  &.workorder-circle_0 {
    background: #e6a23c;
  }

  &.workorder-circle_1 {
    background: #67c23a;
  }

  &.workorder-circle_2 {
    background: #909399;
  }

  &.workorder-circle_3 {
    background: #f99a89;
  }

  &.workorder-circle_4 {
    background: #909399;
  }

  &.workorder-circle_5 {
    background: #67c23a;
  }

  &.workorder-circle_6 {
    background: #67c23a;
  }

  &.color-circle_gray {
    background: #909399;
  }

  &.color-circle_success {
    background: #67c23a;
  }

  &.color-circle_warning {
    background: #F9AD33;
  }

  &.color-circle_danger {
    background: #F55959;
  }

  &.dispose-circle_0 {
    background: #DBE0E7;
  }

  &.dispose-circle_1 {
    background: #FCB950;
  }

  &.dispose-circle_2 {
    background: #4CA474;
  }

  &.dispose-circle_3 {
    background: #0079F4;
  }

  &.dispose-circle_4 {
    background: #E3645F;
  }

  &.dispose-circle_5 {
    background: #FCB950;
  }

  &.dispose-circle_6 {
    background: #4CA474;
  }

  &.dispose-circle_7 {
    background: #FCB950;
  }

  &.api-status-circle_1 {
    background: #67c23a;
  }

  &.api-status-circle_2 {
    background: #FCB950;
  }

  &.api-status-circle_3 {
    background: #909399;
  }

  &.api-status-circle_4 {
    background: #0079F4;
  }
}

// 通用抽屉样式
.qz-api-drawer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  color: #2c3e50;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    position: relative;
    padding: 10px 0;
    border-bottom: 1px solid @border-color;

    &__title {
      padding: 0 20px;
      color: @drawer-title-color;
      font-weight: 500;
      font-size: 16px;
    }

    &__close {
      color: @drawer-close-font-color;
      font-size: 22px;
      line-height: 22px;
      cursor: pointer;
      position: relative;
      margin-right: 20px;
      display: flex;
      align-items: center;
    }
  }

  &__body {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    padding: 15px 20px;
  }

  &__footer {
    padding: 10px 20px;
    display: flex;
    justify-content: flex-end;
    box-sizing: border-box;
    background: @drawer-footer-bg-color;
    border: 1px solid @border-color;
    box-shadow: @drawer-footer-box-shadow;
  }
}

.event-handler-drawer {
  .el-drawer__body {
    padding: 0 !important;
    margin-bottom: 0 !important;
  }
}

.order-dialog {
  .qz-alert__body {
    max-height: 100%;
  }

  .qz-alert__body__main {
    max-height: 100%;
  }
}

// 隐藏日期时间选择器清空按钮
.el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
  display: none;
}

.el-message,
.el-message--success,
.el-message--error {
  top: 40px !important
}

//统一级联选择器箭头图标
.el-cascader .el-icon-arrow-down:before,
.elp-cascader .el-icon-arrow-down:before {
  content: "\e790" !important;
}

// 统一级联选择器字体大小
.elp-cascader__search-input {
  font-size: 14px !important;
}
//设置span样式
.span-normal{
  color:@Color_main_1;
  cursor: pointer;
}
.span-danger{
  color: #f43900;
  cursor: pointer;
}

.y-scroll {
  overflow-y: auto;
}
.p10{
  padding: 10px
}
.p20{
  padding: 20px
}
.pl10{
  padding-left: 10px
}
.pl20{
  padding-left: 20px
}
.pr10{
  padding-right: 10px
}
.pt10{
  padding-top: 10px
}
.pb10{
  padding-bottom: 10px
}
.width-full{
  width: 100% !important;
}
.bg-gray {
  padding: 20px 20px 0;
  margin: 10px 0 0;
  height: 72px;
  background-color: #f9f9f9;
  > div.el-form-item {
    width: 100% !important;
    display: flex;
    /deep/.el-form-item__content {
      display: flex;
      width: auto;
      flex: 1;
      .el-input__inner {
        border-radius: 0;
      }
      .el-select {
        width: 200px;
      }
      .el-date-editor {
        width: 50%;
      }
    }
  }
}

.top-level {
  z-index: 9999;
}
.top-level.el-select-dropdown {
  z-index: 9999 !important;
}