import { getTree<PERSON>nces<PERSON>, register<PERSON><PERSON><PERSON>, ScopedContext } from 'amis-core';
import React from 'react';
import '../styles/filter-form.less';
import { cloneDeep } from 'lodash-es';
import moment from 'moment';

function noUndefined(obj) {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => value !== undefined)
  );
}

class FilterForm extends React.Component {
  static contextType = ScopedContext;

  constructor(props) {
    super(props);
    this.handleFormItemChange = this.handleFormItemChange.bind(this);
  }

  renderActions() {
    const { actions, render } = this.props;

    return (
      <div className="flex-row align-items-center flex-end">
        {actions.map((action, index) => {
          return render('action', action, {
            key: index,
            onAction: this.handleAction
          });
        })}
      </div>
    );
  }

  handleAction = (e, action) => {
    if (action.type === 'submit') {
      this.handleSubmit(e);
    } else if (action.type === 'reset') {
      this.handleReset(e);
    }
  };

  handleSubmit = (e) => {
    e.preventDefault();
    const { onSubmit, store } = this.props;
    if (onSubmit) {
      onSubmit(noUndefined(cloneDeep(store.filterFormData)));
    } else {
      console.warn('onSubmit function is not provided');
    }
  };

  handleReset = (e) => {
    const { onReset, store } = this.props;

    e.preventDefault();

    // 重置表单数据
    store.resetFilterFormData();

    if (onReset) {
      onReset(noUndefined(cloneDeep(store.filterFormData)));
    } else {
      console.warn('onReset function is not provided');
    }
  };

  renderBody() {
    const { body, render, store } = this.props;

    return body.map((item, index) => {
      return render(`body/${index}`, item, {
        key: index,
        // 使用data而不是value来传值，保证重置filterFormData后能够被响应式更新
        data: store.filterFormData,
        className: 'ApiTable__filter-form-item',
        popOverContainer: document.body,
        onChange: (value, name) => {
          this.handleFormItemChange({
            value,
            name,
            compSchema: item
          });
        },
        onIgnoreCaseChange: (ignoreCase) => {
          this.handleFormItemChange({
            value: ignoreCase,
            name: `${item.name}_ignoreCase`,
            compSchema: item
          });
        },
        onOperatorChange: (operator) => {
          this.handleFormItemChange({
            value: operator,
            name: `${item.name}_operator`,
            compSchema: item
          });
        }
      });
    });
  }

  handleFormItemChange({ value, name, compSchema }) {
    const { store } = this.props;
    store.changeFilterFormData(name, value);
    this.changeFilterTags(compSchema);
  }

  changeFilterTags(compSchema) {
    const { store } = this.props;
    const scoped = this.context;
    const value = store.filterFormData[compSchema.name];

    if (value === undefined || value === null || value === '') {
      store.removeFilterTag(compSchema.name);
    } else {
      const tag = {
        name: compSchema.name,
        label: ''
      };

      // 根据组件类型处理标签的显示内容
      const comp = scoped.getComponentById(compSchema.id);
      switch (compSchema.type) {
        case 'switch':
          if (!value) return; // 如果值为false，不显示标签
          tag.label = `${compSchema.label}：是`;
          break;
        case 'crud-input-ignore-case':
          tag.label = `${compSchema.label}：${value}${store.filterFormData[`${compSchema.name}_ignoreCase`] ? '(忽略大小写)' : ''}`;
          break;
        case 'filter-form-select':
        case 'filter-form-radios':
        case 'filter-form-checkboxes':
        case 'filter-form-cascader':
          if (comp) {
            const labelField = compSchema.labelField || 'label';
            const options = comp.props.options || [];
            const selectedOptions = comp.props.selectedOptions || [];
            const tagLabels = selectedOptions.map((option) => {
              const ancestors = getTreeAncestors(options, option, true);
              return ancestors
                .map((item) => item[labelField || 'label'])
                .join('/');
            });

            const { showOperator, operators } = compSchema;
            const operatorValue =
              store.filterFormData[`${compSchema.name}_operator`];
            const operatorLabel =
              operators?.find((op) => op.value === operatorValue)?.label || '';
            tag.label = `${compSchema.label}${showOperator && operatorLabel ? `(${operatorLabel})` : ''}：${tagLabels.join(',')}`;
          } else {
            return; // 如果组件不存在，直接返回
          }
          break;
        case 'input-date':
        case 'input-datetime':
        case 'input-time':
        case 'input-month':
        case 'input-quarter':
        case 'input-year':
        case 'input-date-range':
        case 'input-datetime-range':
        case 'input-time-range':
        case 'input-month-range':
        case 'input-quarter-range':
        case 'input-year-range':
          if (compSchema) {
            const valueFormat = compSchema.valueFormat || 'X';
            const displayFormat = compSchema.displayFormat || 'YYYY-MM-DD';
            const values = Array.isArray(value) ? value : value.split(',');
            const formattedValues = values.map((val) =>
              moment(val, valueFormat).format(displayFormat)
            );
            tag.label = `${compSchema.label}：${formattedValues.join(' ~~ ')}`;
          }
          break;
        default:
          // 对于其他类型的组件，直接使用label和value拼接
          tag.label = `${compSchema.label}：${value}`;
          break;
      }
      store.updateFilterTag(tag);
    }
  }

  render() {
    const { labelWidth } = this.props;

    return (
      <form
        className="ApiTable__filter-form-wrapper"
        style={{
          '--Form-label-width': labelWidth
        }}
        onSubmit={this.handleSubmit}
        onReset={this.handleReset}
      >
        <div className="ApiTable__filter-form-body">{this.renderBody()}</div>
        <div className="ApiTable__filter-form-footer">
          {this.renderActions()}
        </div>
      </form>
    );
  }
}

registerRenderer({
  type: 'crud-filter-form',
  component: FilterForm
});
